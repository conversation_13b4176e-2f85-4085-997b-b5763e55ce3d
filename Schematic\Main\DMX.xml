<?xml version="1.0" encoding="UTF-8"?>
<export version="E">
  <design>
    <source>F:\Projekte\Lichtpult\Firmware\DMX G441\Schematic\Main\DMX.kicad_sch</source>
    <date>29.05.2023 10:23:55</date>
    <tool>Eeschema 7.0.2</tool>
    <sheet number="1" name="/" tstamps="/">
      <title_block>
        <title/>
        <company/>
        <rev/>
        <date/>
        <source>DMX.kicad_sch</source>
        <comment number="1" value=""/>
        <comment number="2" value=""/>
        <comment number="3" value=""/>
        <comment number="4" value=""/>
        <comment number="5" value=""/>
        <comment number="6" value=""/>
        <comment number="7" value=""/>
        <comment number="8" value=""/>
        <comment number="9" value=""/>
      </title_block>
    </sheet>
  </design>
  <components>
    <comp ref="C1">
      <value>1μF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d7291c6</tstamps>
    </comp>
    <comp ref="C2">
      <value>1μF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>f245f049-4f7b-41a2-ac2a-6adc8cd10528</tstamps>
    </comp>
    <comp ref="C3">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>a430060c-76ed-4d4b-8668-873762af6b41</tstamps>
    </comp>
    <comp ref="C4">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d7ae069</tstamps>
    </comp>
    <comp ref="C5">
      <value>20pF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d879c6d</tstamps>
    </comp>
    <comp ref="C6">
      <value>20pF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d87955a</tstamps>
    </comp>
    <comp ref="C7">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d74e1d5</tstamps>
    </comp>
    <comp ref="C8">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d74fad9</tstamps>
    </comp>
    <comp ref="C9">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d75005e</tstamps>
    </comp>
    <comp ref="C10">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d75037d</tstamps>
    </comp>
    <comp ref="C11">
      <value>10nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d750507</tstamps>
    </comp>
    <comp ref="C12">
      <value>4.7μF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d750742</tstamps>
    </comp>
    <comp ref="C13">
      <value>1μF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d750964</tstamps>
    </comp>
    <comp ref="C14">
      <value>100nF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>c9177183-7e1a-4763-a614-15f899781194</tstamps>
    </comp>
    <comp ref="C15">
      <value>1μF</value>
      <footprint>Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="C" description="Unpolarized capacitor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Unpolarized capacitor"/>
      <property name="ki_keywords" value="cap capacitor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>f03ac4de-fb11-4cd4-8cdb-0b356b569ac6</tstamps>
    </comp>
    <comp ref="D5">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ebf73</tstamps>
    </comp>
    <comp ref="D6">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ede53</tstamps>
    </comp>
    <comp ref="D7">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ecbaa</tstamps>
    </comp>
    <comp ref="D8">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ee4fb</tstamps>
    </comp>
    <comp ref="D9">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ed2a1</tstamps>
    </comp>
    <comp ref="D10">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617eeb1e</tstamps>
    </comp>
    <comp ref="D11">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617edb91</tstamps>
    </comp>
    <comp ref="D12">
      <value>D_TVS</value>
      <footprint>Diode_SMD:D_SMA_Handsoldering</footprint>
      <libsource lib="Device" part="D_TVS" description="Bidirectional transient-voltage-suppression diode"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Bidirectional transient-voltage-suppression diode"/>
      <property name="ki_keywords" value="diode TVS thyrector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000617ef21a</tstamps>
    </comp>
    <comp ref="J1">
      <value>USB_C_Receptacle_USB2.0</value>
      <footprint>Connector_USB:USB_C_Receptacle_HRO_TYPE-C-31-M-12</footprint>
      <datasheet>https://www.usb.org/sites/default/files/documents/usb_type-c.zip</datasheet>
      <libsource lib="Connector" part="USB_C_Receptacle_USB2.0" description="USB 2.0-only Type-C Receptacle connector"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="USB 2.0-only Type-C Receptacle connector"/>
      <property name="ki_keywords" value="usb universal serial bus type-C USB2.0"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>9b6f06b6-8cc8-4aa3-ae2e-e4e33a47a4f2</tstamps>
    </comp>
    <comp ref="J2">
      <value>Subboard</value>
      <footprint>Connector_FFC-FPC:TE_1-84953-0_1x10-1MP_P1.0mm_Horizontal</footprint>
      <libsource lib="Connector" part="Conn_01x10_Pin" description="Generic connector, single row, 01x10, script generated"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic connector, single row, 01x10, script generated"/>
      <property name="ki_keywords" value="connector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>bf66aad4-69bb-4913-b571-4bb17185d5aa</tstamps>
    </comp>
    <comp ref="J3">
      <value>DMX</value>
      <footprint>TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm</footprint>
      <libsource lib="Connector" part="Screw_Terminal_01x03" description="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="ki_keywords" value="screw terminal"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d6bdb2f</tstamps>
    </comp>
    <comp ref="J4">
      <value>DMX</value>
      <footprint>TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm</footprint>
      <libsource lib="Connector" part="Screw_Terminal_01x03" description="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="ki_keywords" value="screw terminal"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d6b8cc6</tstamps>
    </comp>
    <comp ref="J5">
      <value>DMX</value>
      <footprint>TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm</footprint>
      <libsource lib="Connector" part="Screw_Terminal_01x03" description="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="ki_keywords" value="screw terminal"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4eedb0</tstamps>
    </comp>
    <comp ref="J6">
      <value>DMX</value>
      <footprint>TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm</footprint>
      <libsource lib="Connector" part="Screw_Terminal_01x03" description="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="ki_keywords" value="screw terminal"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4edd68</tstamps>
    </comp>
    <comp ref="J9">
      <value>Conn_01x01</value>
      <footprint>MountingHole:MountingHole_4.3mm_M4_DIN965_Pad</footprint>
      <libsource lib="Connector_Generic" part="Conn_01x01" description="Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"/>
      <property name="ki_keywords" value="connector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00006089865e</tstamps>
    </comp>
    <comp ref="JP1">
      <value>Jumper_3_Bridged12</value>
      <footprint>Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical</footprint>
      <libsource lib="Jumper" part="Jumper_3_Bridged12" description="Jumper, 3-pole, pins 1+2 closed/bridged"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Jumper, 3-pole, pins 1+2 closed/bridged"/>
      <property name="ki_keywords" value="Jumper SPDT"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d7d2aab</tstamps>
    </comp>
    <comp ref="R3">
      <value>5K1</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>1b5d0e82-0dcd-4323-af65-b8bd0dec71f8</tstamps>
    </comp>
    <comp ref="R4">
      <value>5K1</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>f98a539b-2265-4434-8a16-015735e69060</tstamps>
    </comp>
    <comp ref="R5">
      <value>4K7</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d79b662</tstamps>
    </comp>
    <comp ref="R7">
      <value>130</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000061e4b5f4</tstamps>
    </comp>
    <comp ref="R8">
      <value>130</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000061e4be22</tstamps>
    </comp>
    <comp ref="R9">
      <value>130</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000061e4c38f</tstamps>
    </comp>
    <comp ref="R10">
      <value>130</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000061e4c9b0</tstamps>
    </comp>
    <comp ref="R11">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000060bf2c1b</tstamps>
    </comp>
    <comp ref="R12">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000060bf1a12</tstamps>
    </comp>
    <comp ref="R15">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000609a6e97</tstamps>
    </comp>
    <comp ref="R16">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000609a4d4b</tstamps>
    </comp>
    <comp ref="R19">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000060bf37ad</tstamps>
    </comp>
    <comp ref="R20">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-000060bf2569</tstamps>
    </comp>
    <comp ref="R21">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-0000609a66db</tstamps>
    </comp>
    <comp ref="R22">
      <value>560</value>
      <footprint>Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder</footprint>
      <libsource lib="Device" part="R" description="Resistor"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Resistor"/>
      <property name="ki_keywords" value="R res resistor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00006097b4f4</tstamps>
    </comp>
    <comp ref="SW1">
      <value>SW_Push</value>
      <footprint>Button_Switch_THT:SW_PUSH_6mm</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d7abd55</tstamps>
    </comp>
    <comp ref="SW2">
      <value>SW_DIP_x04</value>
      <footprint>Package_DIP:DIP-8_W7.62mm</footprint>
      <libsource lib="Switch" part="SW_DIP_x04" description="4x DIP Switch, Single Pole Single Throw (SPST) switch, small symbol"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="4x DIP Switch, Single Pole Single Throw (SPST) switch, small symbol"/>
      <property name="ki_keywords" value="dip switch"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>04765940-77d6-4b33-9c21-10ac3f1e29f0</tstamps>
    </comp>
    <comp ref="SW3">
      <value>SW_Push</value>
      <footprint>Button_Switch_THT:SW_Tactile_SPST_Angled_PTS645Vx39-2LFS</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>4e49d060-473d-4eb0-8889-e59f13ab597d</tstamps>
    </comp>
    <comp ref="U1">
      <value>ESDALD05UD4</value>
      <footprint>Package_TO_SOT_SMD:SOT-23-6_Handsoldering</footprint>
      <datasheet>http://www.onsemi.com/pub_link/Collateral/NUP4202W1-D.PDF</datasheet>
      <libsource lib="Power_Protection" part="NUP4202" description="Transient voltage suppressor designed to protect high speed data lines from ESD, EFT, and lightning"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Transient voltage suppressor designed to protect high speed data lines from ESD, EFT, and lightning"/>
      <property name="ki_keywords" value="ESD Protection diodes  transient suppressor"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>58190292-03f0-4fce-8e91-61b7eeb5dc5f</tstamps>
    </comp>
    <comp ref="U2">
      <value>AP2112K-3.3</value>
      <footprint>Package_TO_SOT_SMD:SOT-23-5_HandSoldering</footprint>
      <datasheet>https://www.diodes.com/assets/Datasheets/AP2112.pdf</datasheet>
      <libsource lib="Regulator_Linear" part="AP2112K-3.3" description="600mA low dropout linear regulator, with enable pin, 3.8V-6V input voltage range, 3.3V fixed positive output, SOT-23-5"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="600mA low dropout linear regulator, with enable pin, 3.8V-6V input voltage range, 3.3V fixed positive output, SOT-23-5"/>
      <property name="ki_keywords" value="linear regulator ldo fixed positive"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>ebda82ae-364f-4e99-aa6b-0858734a8248</tstamps>
    </comp>
    <comp ref="U3">
      <value>STM32G474RET</value>
      <footprint>Package_QFP:LQFP-64_10x10mm_P0.5mm</footprint>
      <datasheet>https://cdn-reichelt.de/documents/datenblatt/A300/STM32G441XB.pdf</datasheet>
      <libsource lib="CustomParts" part="STM32G441RB" description=""/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>8a5e8b89-c319-442f-a653-f87e9da317ff</tstamps>
    </comp>
    <comp ref="U5">
      <value>MAX3440E</value>
      <footprint>Package_SO:SOIC-8_3.9x4.9mm_P1.27mm</footprint>
      <datasheet>https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</datasheet>
      <libsource lib="Interface_UART" part="MAX481E" description="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="ki_keywords" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4cf9da</tstamps>
    </comp>
    <comp ref="U6">
      <value>MAX3440E</value>
      <footprint>Package_SO:SOIC-8_3.9x4.9mm_P1.27mm</footprint>
      <datasheet>https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</datasheet>
      <libsource lib="Interface_UART" part="MAX481E" description="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="ki_keywords" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4d012b</tstamps>
    </comp>
    <comp ref="U7">
      <value>MAX3440E</value>
      <footprint>Package_SO:SOIC-8_3.9x4.9mm_P1.27mm</footprint>
      <datasheet>https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</datasheet>
      <libsource lib="Interface_UART" part="MAX481E" description="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="ki_keywords" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4c7897</tstamps>
    </comp>
    <comp ref="U8">
      <value>MAX3440E</value>
      <footprint>Package_SO:SOIC-8_3.9x4.9mm_P1.27mm</footprint>
      <datasheet>https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</datasheet>
      <libsource lib="Interface_UART" part="MAX481E" description="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <property name="ki_keywords" value="Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d4c99a2</tstamps>
    </comp>
    <comp ref="Y1">
      <value>25MHz</value>
      <footprint>Crystal:Crystal_HC18-U_Vertical</footprint>
      <libsource lib="Device" part="Crystal" description="Two pin crystal"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX.kicad_sch"/>
      <property name="ki_description" value="Two pin crystal"/>
      <property name="ki_keywords" value="quartz ceramic resonator oscillator"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>00000000-0000-0000-0000-00005d84ea8a</tstamps>
    </comp>
  </components>
  <libparts>
    <libpart lib="Connector" part="Conn_01x06_Male">
      <description>Generic connector, single row, 01x06, script generated (kicad-library-utils/schlib/autogen/connector/)</description>
      <docs>~</docs>
      <footprints>
        <fp>Connector*:*_1x??_*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Conn_01x06_Male</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
        <pin num="2" name="Pin_2" type="passive"/>
        <pin num="3" name="Pin_3" type="passive"/>
        <pin num="4" name="Pin_4" type="passive"/>
        <pin num="5" name="Pin_5" type="passive"/>
        <pin num="6" name="Pin_6" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Connector" part="Conn_01x10_Pin">
      <description>Generic connector, single row, 01x10, script generated</description>
      <docs>~</docs>
      <footprints>
        <fp>Connector*:*_1x??_*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Conn_01x10_Pin</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
        <pin num="2" name="Pin_2" type="passive"/>
        <pin num="3" name="Pin_3" type="passive"/>
        <pin num="4" name="Pin_4" type="passive"/>
        <pin num="5" name="Pin_5" type="passive"/>
        <pin num="6" name="Pin_6" type="passive"/>
        <pin num="7" name="Pin_7" type="passive"/>
        <pin num="8" name="Pin_8" type="passive"/>
        <pin num="9" name="Pin_9" type="passive"/>
        <pin num="10" name="Pin_10" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Connector" part="Screw_Terminal_01x03">
      <description>Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)</description>
      <docs>~</docs>
      <footprints>
        <fp>TerminalBlock*:*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Screw_Terminal_01x03</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
        <pin num="2" name="Pin_2" type="passive"/>
        <pin num="3" name="Pin_3" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Connector" part="USB_C_Receptacle_USB2.0">
      <description>USB 2.0-only Type-C Receptacle connector</description>
      <docs>https://www.usb.org/sites/default/files/documents/usb_type-c.zip</docs>
      <footprints>
        <fp>USB*C*Receptacle*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">USB_C_Receptacle_USB2.0</field>
        <field name="Datasheet">https://www.usb.org/sites/default/files/documents/usb_type-c.zip</field>
      </fields>
      <pins>
        <pin num="A1" name="GND" type="passive"/>
        <pin num="A4" name="VBUS" type="passive"/>
        <pin num="A5" name="CC1" type="bidirectional"/>
        <pin num="A6" name="D+" type="bidirectional"/>
        <pin num="A7" name="D-" type="bidirectional"/>
        <pin num="A8" name="SBU1" type="bidirectional"/>
        <pin num="A9" name="VBUS" type="passive"/>
        <pin num="A12" name="GND" type="passive"/>
        <pin num="B1" name="GND" type="passive"/>
        <pin num="B4" name="VBUS" type="passive"/>
        <pin num="B5" name="CC2" type="bidirectional"/>
        <pin num="B6" name="D+" type="bidirectional"/>
        <pin num="B7" name="D-" type="bidirectional"/>
        <pin num="B8" name="SBU2" type="bidirectional"/>
        <pin num="B9" name="VBUS" type="passive"/>
        <pin num="B12" name="GND" type="passive"/>
        <pin num="S1" name="SHIELD" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Connector_Generic" part="Conn_01x01">
      <description>Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)</description>
      <docs>~</docs>
      <footprints>
        <fp>Connector*:*_1x??_*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Conn_01x01</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="CustomParts" part="STM32G441RB">
      <docs>https://cdn-reichelt.de/documents/datenblatt/A300/STM32G441XB.pdf</docs>
      <fields>
        <field name="Reference">U</field>
        <field name="Value">STM32G441RB</field>
        <field name="Footprint">Package_QFP:LQFP-64_10x10mm_P0.5mm</field>
        <field name="Datasheet">https://cdn-reichelt.de/documents/datenblatt/A300/STM32G441XB.pdf</field>
      </fields>
      <pins>
        <pin num="1" name="VBAT" type="power_in"/>
        <pin num="2" name="PC13" type="bidirectional"/>
        <pin num="3" name="PC14" type="bidirectional"/>
        <pin num="4" name="PC15" type="bidirectional"/>
        <pin num="5" name="PF0" type="input"/>
        <pin num="6" name="PF1" type="input"/>
        <pin num="7" name="PG10_NRST" type="input"/>
        <pin num="8" name="PC0" type="bidirectional"/>
        <pin num="9" name="PC1" type="bidirectional"/>
        <pin num="10" name="PC2" type="bidirectional"/>
        <pin num="11" name="PC3" type="bidirectional"/>
        <pin num="12" name="PA0" type="bidirectional"/>
        <pin num="13" name="PA1" type="bidirectional"/>
        <pin num="14" name="PA2" type="bidirectional"/>
        <pin num="15" name="VSS" type="power_in"/>
        <pin num="16" name="VDD" type="power_in"/>
        <pin num="17" name="PA3" type="bidirectional"/>
        <pin num="18" name="PA4" type="bidirectional"/>
        <pin num="19" name="PA5" type="bidirectional"/>
        <pin num="20" name="PA6" type="bidirectional"/>
        <pin num="21" name="PA7" type="bidirectional"/>
        <pin num="22" name="PC4" type="bidirectional"/>
        <pin num="23" name="PC5" type="bidirectional"/>
        <pin num="24" name="PB0" type="bidirectional"/>
        <pin num="25" name="PB1" type="bidirectional"/>
        <pin num="26" name="PB2" type="bidirectional"/>
        <pin num="27" name="VSSA" type="power_in"/>
        <pin num="28" name="VREF+" type="power_in"/>
        <pin num="29" name="VDDA" type="power_in"/>
        <pin num="30" name="PB10" type="bidirectional"/>
        <pin num="31" name="VSS" type="power_in"/>
        <pin num="32" name="VDD" type="power_in"/>
        <pin num="33" name="PB11" type="bidirectional"/>
        <pin num="34" name="PB12" type="bidirectional"/>
        <pin num="35" name="PB13" type="bidirectional"/>
        <pin num="36" name="PB14" type="bidirectional"/>
        <pin num="37" name="PB15" type="bidirectional"/>
        <pin num="38" name="PC6" type="bidirectional"/>
        <pin num="39" name="PC7" type="bidirectional"/>
        <pin num="40" name="PC8" type="bidirectional"/>
        <pin num="41" name="PC9" type="bidirectional"/>
        <pin num="42" name="PA8" type="bidirectional"/>
        <pin num="43" name="PA9" type="bidirectional"/>
        <pin num="44" name="PA10" type="bidirectional"/>
        <pin num="45" name="PA11" type="bidirectional"/>
        <pin num="46" name="PA12" type="bidirectional"/>
        <pin num="47" name="VSS" type="power_in"/>
        <pin num="48" name="VDD" type="power_in"/>
        <pin num="49" name="PA13" type="bidirectional"/>
        <pin num="50" name="PA14" type="bidirectional"/>
        <pin num="51" name="PA15" type="bidirectional"/>
        <pin num="52" name="PC10" type="bidirectional"/>
        <pin num="53" name="PC11" type="bidirectional"/>
        <pin num="54" name="PC12" type="bidirectional"/>
        <pin num="55" name="PD2" type="bidirectional"/>
        <pin num="56" name="PB3" type="bidirectional"/>
        <pin num="57" name="PB4" type="bidirectional"/>
        <pin num="58" name="PB5" type="bidirectional"/>
        <pin num="59" name="PB6" type="bidirectional"/>
        <pin num="60" name="PB7" type="bidirectional"/>
        <pin num="61" name="PB8_BOOT0" type="bidirectional"/>
        <pin num="62" name="PB9" type="bidirectional"/>
        <pin num="63" name="VSS" type="power_in"/>
        <pin num="64" name="VDD" type="power_in"/>
      </pins>
    </libpart>
    <libpart lib="Device" part="C">
      <description>Unpolarized capacitor</description>
      <docs>~</docs>
      <footprints>
        <fp>C_*</fp>
      </footprints>
      <fields>
        <field name="Reference">C</field>
        <field name="Value">C</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="" type="passive"/>
        <pin num="2" name="" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Device" part="Crystal">
      <description>Two pin crystal</description>
      <docs>~</docs>
      <footprints>
        <fp>Crystal*</fp>
      </footprints>
      <fields>
        <field name="Reference">Y</field>
        <field name="Value">Crystal</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="1" type="passive"/>
        <pin num="2" name="2" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Device" part="D_TVS">
      <description>Bidirectional transient-voltage-suppression diode</description>
      <docs>~</docs>
      <footprints>
        <fp>TO-???*</fp>
        <fp>*_Diode_*</fp>
        <fp>*SingleDiode*</fp>
        <fp>D_*</fp>
      </footprints>
      <fields>
        <field name="Reference">D</field>
        <field name="Value">D_TVS</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="A1" type="passive"/>
        <pin num="2" name="A2" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Device" part="R">
      <description>Resistor</description>
      <docs>~</docs>
      <footprints>
        <fp>R_*</fp>
      </footprints>
      <fields>
        <field name="Reference">R</field>
        <field name="Value">R</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="" type="passive"/>
        <pin num="2" name="" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Interface_UART" part="MAX481E">
      <description>Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8</description>
      <docs>https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</docs>
      <footprints>
        <fp>DIP*W7.62mm*</fp>
        <fp>SOIC*3.9x4.9mm*P1.27mm*</fp>
      </footprints>
      <fields>
        <field name="Reference">U</field>
        <field name="Value">MAX481E</field>
        <field name="Datasheet">https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf</field>
      </fields>
      <pins>
        <pin num="1" name="RO" type="output"/>
        <pin num="2" name="~{RE}" type="input"/>
        <pin num="3" name="DE" type="input"/>
        <pin num="4" name="DI" type="input"/>
        <pin num="5" name="GND" type="power_in"/>
        <pin num="6" name="A" type="bidirectional"/>
        <pin num="7" name="B" type="bidirectional"/>
        <pin num="8" name="VCC" type="power_in"/>
      </pins>
    </libpart>
    <libpart lib="Jumper" part="Jumper_3_Bridged12">
      <description>Jumper, 3-pole, pins 1+2 closed/bridged</description>
      <docs>~</docs>
      <footprints>
        <fp>Jumper*</fp>
        <fp>TestPoint*3Pads*</fp>
        <fp>TestPoint*Bridge*</fp>
      </footprints>
      <fields>
        <field name="Reference">JP</field>
        <field name="Value">Jumper_3_Bridged12</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="A" type="passive"/>
        <pin num="2" name="C" type="input"/>
        <pin num="3" name="B" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Power_Protection" part="NUP4202">
      <description>Transient voltage suppressor designed to protect high speed data lines from ESD, EFT, and lightning</description>
      <docs>http://www.onsemi.com/pub_link/Collateral/NUP4202W1-D.PDF</docs>
      <footprints>
        <fp>SOT?363*</fp>
      </footprints>
      <fields>
        <field name="Reference">U</field>
        <field name="Value">NUP4202</field>
        <field name="Footprint">Package_TO_SOT_SMD:SOT-363_SC-70-6</field>
        <field name="Datasheet">http://www.onsemi.com/pub_link/Collateral/NUP4202W1-D.PDF</field>
      </fields>
      <pins>
        <pin num="1" name="" type="passive"/>
        <pin num="2" name="" type="passive"/>
        <pin num="3" name="" type="passive"/>
        <pin num="4" name="" type="passive"/>
        <pin num="5" name="" type="passive"/>
        <pin num="6" name="" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Regulator_Linear" part="AP2112K-3.3">
      <description>600mA low dropout linear regulator, with enable pin, 3.8V-6V input voltage range, 3.3V fixed positive output, SOT-23-5</description>
      <docs>https://www.diodes.com/assets/Datasheets/AP2112.pdf</docs>
      <footprints>
        <fp>SOT?23?5*</fp>
      </footprints>
      <fields>
        <field name="Reference">U</field>
        <field name="Value">AP2112K-3.3</field>
        <field name="Footprint">Package_TO_SOT_SMD:SOT-23-5</field>
        <field name="Datasheet">https://www.diodes.com/assets/Datasheets/AP2112.pdf</field>
      </fields>
      <pins>
        <pin num="1" name="VIN" type="power_in"/>
        <pin num="2" name="GND" type="power_in"/>
        <pin num="3" name="EN" type="input"/>
        <pin num="4" name="NC" type="no_connect"/>
        <pin num="5" name="VOUT" type="power_out"/>
      </pins>
    </libpart>
    <libpart lib="Switch" part="SW_DIP_x04">
      <description>4x DIP Switch, Single Pole Single Throw (SPST) switch, small symbol</description>
      <docs>~</docs>
      <footprints>
        <fp>SW?DIP?x4*</fp>
      </footprints>
      <fields>
        <field name="Reference">SW</field>
        <field name="Value">SW_DIP_x04</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="" type="passive"/>
        <pin num="2" name="" type="passive"/>
        <pin num="3" name="" type="passive"/>
        <pin num="4" name="" type="passive"/>
        <pin num="5" name="" type="passive"/>
        <pin num="6" name="" type="passive"/>
        <pin num="7" name="" type="passive"/>
        <pin num="8" name="" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Switch" part="SW_Push">
      <description>Push button switch, generic, two pins</description>
      <docs>~</docs>
      <fields>
        <field name="Reference">SW</field>
        <field name="Value">SW_Push</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="1" type="passive"/>
        <pin num="2" name="2" type="passive"/>
      </pins>
    </libpart>
  </libparts>
  <libraries>
    <library logical="Connector">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Connector.kicad_sym</uri>
    </library>
    <library logical="Connector_Generic">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Connector_Generic.kicad_sym</uri>
    </library>
    <library logical="CustomParts">
      <uri>C:/Users/<USER>/Documents/KiCad/7.0/symbols/CustomParts.kicad_sym</uri>
    </library>
    <library logical="Device">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Device.kicad_sym</uri>
    </library>
    <library logical="Interface_UART">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Interface_UART.kicad_sym</uri>
    </library>
    <library logical="Jumper">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Jumper.kicad_sym</uri>
    </library>
    <library logical="Power_Protection">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Power_Protection.kicad_sym</uri>
    </library>
    <library logical="Regulator_Linear">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Regulator_Linear.kicad_sym</uri>
    </library>
    <library logical="Switch">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Switch.kicad_sym</uri>
    </library>
  </libraries>
  <nets>
    <net code="1" name="+3V3">
      <node ref="C10" pin="2" pintype="passive"/>
      <node ref="C11" pin="2" pintype="passive"/>
      <node ref="C12" pin="2" pintype="passive"/>
      <node ref="C13" pin="2" pintype="passive"/>
      <node ref="C14" pin="2" pintype="passive"/>
      <node ref="C15" pin="1" pintype="passive"/>
      <node ref="C2" pin="1" pintype="passive"/>
      <node ref="C3" pin="1" pintype="passive"/>
      <node ref="C7" pin="2" pintype="passive"/>
      <node ref="C8" pin="2" pintype="passive"/>
      <node ref="C9" pin="2" pintype="passive"/>
      <node ref="J2" pin="4" pinfunction="Pin_4" pintype="passive"/>
      <node ref="JP1" pin="3" pinfunction="B" pintype="passive"/>
      <node ref="R19" pin="1" pintype="passive"/>
      <node ref="R20" pin="1" pintype="passive"/>
      <node ref="R21" pin="2" pintype="passive"/>
      <node ref="R22" pin="2" pintype="passive"/>
      <node ref="R5" pin="1" pintype="passive"/>
      <node ref="SW2" pin="5" pintype="passive"/>
      <node ref="SW2" pin="6" pintype="passive"/>
      <node ref="SW2" pin="7" pintype="passive"/>
      <node ref="SW2" pin="8" pintype="passive"/>
      <node ref="SW3" pin="1" pinfunction="1" pintype="passive"/>
      <node ref="U2" pin="5" pinfunction="VOUT" pintype="power_out"/>
      <node ref="U3" pin="16" pinfunction="VDD" pintype="power_in"/>
      <node ref="U3" pin="28" pinfunction="VREF+" pintype="power_in"/>
      <node ref="U3" pin="29" pinfunction="VDDA" pintype="power_in"/>
      <node ref="U3" pin="32" pinfunction="VDD" pintype="power_in"/>
      <node ref="U3" pin="48" pinfunction="VDD" pintype="power_in"/>
      <node ref="U3" pin="64" pinfunction="VDD" pintype="power_in"/>
      <node ref="U5" pin="8" pinfunction="VCC" pintype="power_in"/>
      <node ref="U6" pin="8" pinfunction="VCC" pintype="power_in"/>
      <node ref="U7" pin="8" pinfunction="VCC" pintype="power_in"/>
      <node ref="U8" pin="8" pinfunction="VCC" pintype="power_in"/>
    </net>
    <net code="2" name="+5V">
      <node ref="C1" pin="1" pintype="passive"/>
      <node ref="J1" pin="A4" pinfunction="VBUS" pintype="passive"/>
      <node ref="J1" pin="A9" pinfunction="VBUS" pintype="passive"/>
      <node ref="J1" pin="B4" pinfunction="VBUS" pintype="passive"/>
      <node ref="J1" pin="B9" pinfunction="VBUS" pintype="passive"/>
      <node ref="U1" pin="5" pintype="passive"/>
      <node ref="U2" pin="1" pinfunction="VIN" pintype="power_in"/>
      <node ref="U2" pin="3" pinfunction="EN" pintype="input"/>
    </net>
    <net code="3" name="/SWCLK">
      <node ref="U3" pin="50" pinfunction="PA14" pintype="bidirectional"/>
    </net>
    <net code="4" name="/SWDIO">
      <node ref="U3" pin="49" pinfunction="PA13" pintype="bidirectional"/>
    </net>
    <net code="6" name="DMX1_DE">
      <node ref="U3" pin="51" pinfunction="PA15" pintype="bidirectional"/>
      <node ref="U6" pin="2" pinfunction="~{RE}" pintype="input"/>
      <node ref="U6" pin="3" pinfunction="DE" pintype="input"/>
    </net>
    <net code="7" name="DMX1_RX">
      <node ref="U3" pin="53" pinfunction="PC11" pintype="bidirectional"/>
      <node ref="U6" pin="1" pinfunction="RO" pintype="output"/>
    </net>
    <net code="8" name="DMX1_TX">
      <node ref="U3" pin="52" pinfunction="PC10" pintype="bidirectional"/>
      <node ref="U6" pin="4" pinfunction="DI" pintype="input"/>
    </net>
    <net code="9" name="DMX2_DE">
      <node ref="U3" pin="34" pinfunction="PB12" pintype="bidirectional"/>
      <node ref="U5" pin="2" pinfunction="~{RE}" pintype="input"/>
      <node ref="U5" pin="3" pinfunction="DE" pintype="input"/>
    </net>
    <net code="10" name="DMX2_RX">
      <node ref="U3" pin="33" pinfunction="PB11" pintype="bidirectional"/>
      <node ref="U5" pin="1" pinfunction="RO" pintype="output"/>
    </net>
    <net code="11" name="DMX2_TX">
      <node ref="U3" pin="30" pinfunction="PB10" pintype="bidirectional"/>
      <node ref="U5" pin="4" pinfunction="DI" pintype="input"/>
    </net>
    <net code="12" name="DMX3_DE">
      <node ref="U3" pin="58" pinfunction="PB5" pintype="bidirectional"/>
      <node ref="U8" pin="2" pinfunction="~{RE}" pintype="input"/>
      <node ref="U8" pin="3" pinfunction="DE" pintype="input"/>
    </net>
    <net code="13" name="DMX3_RX">
      <node ref="U3" pin="60" pinfunction="PB7" pintype="bidirectional"/>
      <node ref="U8" pin="1" pinfunction="RO" pintype="output"/>
    </net>
    <net code="14" name="DMX3_TX">
      <node ref="U3" pin="59" pinfunction="PB6" pintype="bidirectional"/>
      <node ref="U8" pin="4" pinfunction="DI" pintype="input"/>
    </net>
    <net code="15" name="DMX4_DE">
      <node ref="U3" pin="54" pinfunction="PC12" pintype="bidirectional"/>
      <node ref="U7" pin="2" pinfunction="~{RE}" pintype="input"/>
      <node ref="U7" pin="3" pinfunction="DE" pintype="input"/>
    </net>
    <net code="16" name="DMX4_RX">
      <node ref="U3" pin="57" pinfunction="PB4" pintype="bidirectional"/>
      <node ref="U7" pin="1" pinfunction="RO" pintype="output"/>
    </net>
    <net code="17" name="DMX4_TX">
      <node ref="U3" pin="56" pinfunction="PB3" pintype="bidirectional"/>
      <node ref="U7" pin="4" pinfunction="DI" pintype="input"/>
    </net>
    <net code="18" name="GND">
      <node ref="C1" pin="2" pintype="passive"/>
      <node ref="C10" pin="1" pintype="passive"/>
      <node ref="C11" pin="1" pintype="passive"/>
      <node ref="C12" pin="1" pintype="passive"/>
      <node ref="C13" pin="1" pintype="passive"/>
      <node ref="C15" pin="2" pintype="passive"/>
      <node ref="C2" pin="2" pintype="passive"/>
      <node ref="C3" pin="2" pintype="passive"/>
      <node ref="C4" pin="2" pintype="passive"/>
      <node ref="C5" pin="2" pintype="passive"/>
      <node ref="C6" pin="2" pintype="passive"/>
      <node ref="C7" pin="1" pintype="passive"/>
      <node ref="C8" pin="1" pintype="passive"/>
      <node ref="C9" pin="1" pintype="passive"/>
      <node ref="D10" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D11" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D12" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D5" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D6" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D7" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D8" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="D9" pin="2" pinfunction="A2" pintype="passive"/>
      <node ref="J1" pin="A1" pinfunction="GND" pintype="passive"/>
      <node ref="J1" pin="A12" pinfunction="GND" pintype="passive"/>
      <node ref="J1" pin="B1" pinfunction="GND" pintype="passive"/>
      <node ref="J1" pin="B12" pinfunction="GND" pintype="passive"/>
      <node ref="J1" pin="S1" pinfunction="SHIELD" pintype="passive"/>
      <node ref="J2" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="J3" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="J4" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="J5" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="J6" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="J9" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="JP1" pin="1" pinfunction="A" pintype="passive"/>
      <node ref="R11" pin="2" pintype="passive"/>
      <node ref="R12" pin="2" pintype="passive"/>
      <node ref="R15" pin="2" pintype="passive"/>
      <node ref="R16" pin="2" pintype="passive"/>
      <node ref="R3" pin="2" pintype="passive"/>
      <node ref="R4" pin="2" pintype="passive"/>
      <node ref="SW1" pin="1" pinfunction="1" pintype="passive"/>
      <node ref="U1" pin="2" pintype="passive"/>
      <node ref="U2" pin="2" pinfunction="GND" pintype="power_in"/>
      <node ref="U3" pin="15" pinfunction="VSS" pintype="power_in"/>
      <node ref="U3" pin="27" pinfunction="VSSA" pintype="power_in"/>
      <node ref="U3" pin="31" pinfunction="VSS" pintype="power_in"/>
      <node ref="U3" pin="47" pinfunction="VSS" pintype="power_in"/>
      <node ref="U3" pin="63" pinfunction="VSS" pintype="power_in"/>
      <node ref="U5" pin="5" pinfunction="GND" pintype="power_in"/>
      <node ref="U6" pin="5" pinfunction="GND" pintype="power_in"/>
      <node ref="U7" pin="5" pinfunction="GND" pintype="power_in"/>
      <node ref="U8" pin="5" pinfunction="GND" pintype="power_in"/>
    </net>
    <net code="19" name="NRST">
      <node ref="C4" pin="1" pintype="passive"/>
      <node ref="R5" pin="2" pintype="passive"/>
      <node ref="SW1" pin="2" pinfunction="2" pintype="passive"/>
      <node ref="U3" pin="7" pinfunction="PG10_NRST" pintype="input"/>
    </net>
    <net code="20" name="Net-(D5-A1)">
      <node ref="D5" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J3" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="R19" pin="2" pintype="passive"/>
      <node ref="R7" pin="2" pintype="passive"/>
      <node ref="U6" pin="6" pinfunction="A" pintype="bidirectional"/>
    </net>
    <net code="21" name="Net-(D6-A1)">
      <node ref="D6" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J3" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="R11" pin="1" pintype="passive"/>
      <node ref="R7" pin="1" pintype="passive"/>
      <node ref="U6" pin="7" pinfunction="B" pintype="bidirectional"/>
    </net>
    <net code="22" name="Net-(D7-A1)">
      <node ref="D7" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J4" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="R20" pin="2" pintype="passive"/>
      <node ref="R8" pin="2" pintype="passive"/>
      <node ref="U5" pin="6" pinfunction="A" pintype="bidirectional"/>
    </net>
    <net code="23" name="Net-(D8-A1)">
      <node ref="D8" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J4" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="R12" pin="1" pintype="passive"/>
      <node ref="R8" pin="1" pintype="passive"/>
      <node ref="U5" pin="7" pinfunction="B" pintype="bidirectional"/>
    </net>
    <net code="24" name="Net-(D9-A1)">
      <node ref="D9" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J5" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="R21" pin="1" pintype="passive"/>
      <node ref="R9" pin="2" pintype="passive"/>
      <node ref="U8" pin="6" pinfunction="A" pintype="bidirectional"/>
    </net>
    <net code="25" name="Net-(D10-A1)">
      <node ref="D10" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J5" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="R15" pin="1" pintype="passive"/>
      <node ref="R9" pin="1" pintype="passive"/>
      <node ref="U8" pin="7" pinfunction="B" pintype="bidirectional"/>
    </net>
    <net code="26" name="Net-(D11-A1)">
      <node ref="D11" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J6" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="R10" pin="2" pintype="passive"/>
      <node ref="R22" pin="1" pintype="passive"/>
      <node ref="U7" pin="6" pinfunction="A" pintype="bidirectional"/>
    </net>
    <net code="27" name="Net-(D12-A1)">
      <node ref="D12" pin="1" pinfunction="A1" pintype="passive"/>
      <node ref="J6" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="R10" pin="1" pintype="passive"/>
      <node ref="R16" pin="1" pintype="passive"/>
      <node ref="U7" pin="7" pinfunction="B" pintype="bidirectional"/>
    </net>
    <net code="28" name="Net-(J1-CC1)">
      <node ref="J1" pin="A5" pinfunction="CC1" pintype="bidirectional"/>
      <node ref="R4" pin="1" pintype="passive"/>
    </net>
    <net code="29" name="Net-(J1-CC2)">
      <node ref="J1" pin="B5" pinfunction="CC2" pintype="bidirectional"/>
      <node ref="R3" pin="1" pintype="passive"/>
    </net>
    <net code="30" name="Net-(J2-Pin_5)">
      <node ref="J2" pin="5" pinfunction="Pin_5" pintype="passive"/>
      <node ref="U3" pin="25" pinfunction="PB1" pintype="bidirectional"/>
    </net>
    <net code="31" name="Net-(J2-Pin_6)">
      <node ref="J2" pin="6" pinfunction="Pin_6" pintype="passive"/>
      <node ref="U3" pin="26" pinfunction="PB2" pintype="bidirectional"/>
    </net>
    <net code="32" name="Net-(J2-Pin_7)">
      <node ref="J2" pin="7" pinfunction="Pin_7" pintype="passive"/>
      <node ref="U3" pin="35" pinfunction="PB13" pintype="bidirectional"/>
    </net>
    <net code="33" name="Net-(J2-Pin_8)">
      <node ref="J2" pin="8" pinfunction="Pin_8" pintype="passive"/>
      <node ref="U3" pin="36" pinfunction="PB14" pintype="bidirectional"/>
    </net>
    <net code="34" name="Net-(J2-Pin_9)">
      <node ref="J2" pin="9" pinfunction="Pin_9" pintype="passive"/>
      <node ref="U3" pin="37" pinfunction="PB15" pintype="bidirectional"/>
    </net>
    <net code="35" name="Net-(JP1-C)">
      <node ref="JP1" pin="2" pinfunction="C" pintype="input"/>
      <node ref="U3" pin="61" pinfunction="PB8_BOOT0" pintype="bidirectional"/>
    </net>
    <net code="36" name="Net-(U3-PA0)">
      <node ref="SW2" pin="1" pintype="passive"/>
      <node ref="U3" pin="12" pinfunction="PA0" pintype="bidirectional"/>
    </net>
    <net code="37" name="Net-(U3-PA1)">
      <node ref="SW2" pin="2" pintype="passive"/>
      <node ref="U3" pin="13" pinfunction="PA1" pintype="bidirectional"/>
    </net>
    <net code="38" name="Net-(U3-PA2)">
      <node ref="SW2" pin="3" pintype="passive"/>
      <node ref="U3" pin="14" pinfunction="PA2" pintype="bidirectional"/>
    </net>
    <net code="39" name="Net-(U3-PA3)">
      <node ref="SW2" pin="4" pintype="passive"/>
      <node ref="U3" pin="17" pinfunction="PA3" pintype="bidirectional"/>
    </net>
    <net code="40" name="Net-(U3-PA5)">
      <node ref="C14" pin="1" pintype="passive"/>
      <node ref="SW3" pin="2" pinfunction="2" pintype="passive"/>
      <node ref="U3" pin="19" pinfunction="PA5" pintype="bidirectional"/>
    </net>
    <net code="41" name="Net-(U3-PF0)">
      <node ref="C5" pin="1" pintype="passive"/>
      <node ref="U3" pin="5" pinfunction="PF0" pintype="input"/>
      <node ref="Y1" pin="1" pinfunction="1" pintype="passive"/>
    </net>
    <net code="42" name="Net-(U3-PF1)">
      <node ref="C6" pin="1" pintype="passive"/>
      <node ref="U3" pin="6" pinfunction="PF1" pintype="input"/>
      <node ref="Y1" pin="2" pinfunction="2" pintype="passive"/>
    </net>
    <net code="43" name="SCL">
      <node ref="J2" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="U3" pin="38" pinfunction="PC6" pintype="bidirectional"/>
    </net>
    <net code="44" name="SDA">
      <node ref="J2" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="U3" pin="39" pinfunction="PC7" pintype="bidirectional"/>
    </net>
    <net code="45" name="USB+">
      <node ref="J1" pin="A6" pinfunction="D+" pintype="bidirectional"/>
      <node ref="J1" pin="B6" pinfunction="D+" pintype="bidirectional"/>
      <node ref="U1" pin="6" pintype="passive"/>
      <node ref="U3" pin="46" pinfunction="PA12" pintype="bidirectional"/>
    </net>
    <net code="46" name="USB-">
      <node ref="J1" pin="A7" pinfunction="D-" pintype="bidirectional"/>
      <node ref="J1" pin="B7" pinfunction="D-" pintype="bidirectional"/>
      <node ref="U1" pin="1" pintype="passive"/>
      <node ref="U3" pin="45" pinfunction="PA11" pintype="bidirectional"/>
    </net>
    <net code="47" name="unconnected-(J1-SBU1-PadA8)">
      <node ref="J1" pin="A8" pinfunction="SBU1" pintype="bidirectional+no_connect"/>
    </net>
    <net code="48" name="unconnected-(J1-SBU2-PadB8)">
      <node ref="J1" pin="B8" pinfunction="SBU2" pintype="bidirectional+no_connect"/>
    </net>
    <net code="49" name="unconnected-(J2-Pin_10-Pad10)">
      <node ref="J2" pin="10" pinfunction="Pin_10" pintype="passive"/>
    </net>
    <net code="50" name="unconnected-(U1-Pad3)">
      <node ref="U1" pin="3" pintype="passive"/>
    </net>
    <net code="51" name="unconnected-(U1-Pad4)">
      <node ref="U1" pin="4" pintype="passive"/>
    </net>
    <net code="52" name="unconnected-(U2-NC-Pad4)">
      <node ref="U2" pin="4" pinfunction="NC" pintype="no_connect"/>
    </net>
    <net code="53" name="unconnected-(U3-PA4-Pad18)">
      <node ref="U3" pin="18" pinfunction="PA4" pintype="bidirectional"/>
    </net>
    <net code="54" name="unconnected-(U3-PA6-Pad20)">
      <node ref="U3" pin="20" pinfunction="PA6" pintype="bidirectional"/>
    </net>
    <net code="55" name="unconnected-(U3-PA7-Pad21)">
      <node ref="U3" pin="21" pinfunction="PA7" pintype="bidirectional"/>
    </net>
    <net code="56" name="unconnected-(U3-PA8-Pad42)">
      <node ref="U3" pin="42" pinfunction="PA8" pintype="bidirectional"/>
    </net>
    <net code="57" name="unconnected-(U3-PA9-Pad43)">
      <node ref="U3" pin="43" pinfunction="PA9" pintype="bidirectional"/>
    </net>
    <net code="58" name="unconnected-(U3-PA10-Pad44)">
      <node ref="U3" pin="44" pinfunction="PA10" pintype="bidirectional"/>
    </net>
    <net code="59" name="unconnected-(U3-PB0-Pad24)">
      <node ref="U3" pin="24" pinfunction="PB0" pintype="bidirectional"/>
    </net>
    <net code="60" name="unconnected-(U3-PB9-Pad62)">
      <node ref="U3" pin="62" pinfunction="PB9" pintype="bidirectional"/>
    </net>
    <net code="61" name="unconnected-(U3-PC0-Pad8)">
      <node ref="U3" pin="8" pinfunction="PC0" pintype="bidirectional"/>
    </net>
    <net code="62" name="unconnected-(U3-PC1-Pad9)">
      <node ref="U3" pin="9" pinfunction="PC1" pintype="bidirectional"/>
    </net>
    <net code="63" name="unconnected-(U3-PC2-Pad10)">
      <node ref="U3" pin="10" pinfunction="PC2" pintype="bidirectional"/>
    </net>
    <net code="64" name="unconnected-(U3-PC3-Pad11)">
      <node ref="U3" pin="11" pinfunction="PC3" pintype="bidirectional"/>
    </net>
    <net code="65" name="unconnected-(U3-PC4-Pad22)">
      <node ref="U3" pin="22" pinfunction="PC4" pintype="bidirectional"/>
    </net>
    <net code="66" name="unconnected-(U3-PC5-Pad23)">
      <node ref="U3" pin="23" pinfunction="PC5" pintype="bidirectional"/>
    </net>
    <net code="67" name="unconnected-(U3-PC8-Pad40)">
      <node ref="U3" pin="40" pinfunction="PC8" pintype="bidirectional"/>
    </net>
    <net code="68" name="unconnected-(U3-PC9-Pad41)">
      <node ref="U3" pin="41" pinfunction="PC9" pintype="bidirectional"/>
    </net>
    <net code="69" name="unconnected-(U3-PC13-Pad2)">
      <node ref="U3" pin="2" pinfunction="PC13" pintype="bidirectional"/>
    </net>
    <net code="70" name="unconnected-(U3-PC14-Pad3)">
      <node ref="U3" pin="3" pinfunction="PC14" pintype="bidirectional"/>
    </net>
    <net code="71" name="unconnected-(U3-PC15-Pad4)">
      <node ref="U3" pin="4" pinfunction="PC15" pintype="bidirectional"/>
    </net>
    <net code="72" name="unconnected-(U3-PD2-Pad55)">
      <node ref="U3" pin="55" pinfunction="PD2" pintype="bidirectional"/>
    </net>
    <net code="73" name="unconnected-(U3-VBAT-Pad1)">
      <node ref="U3" pin="1" pinfunction="VBAT" pintype="power_in"/>
    </net>
  </nets>
</export>
