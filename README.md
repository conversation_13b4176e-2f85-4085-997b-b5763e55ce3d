# Steady Node
Steady Node is a compact, two-universe CRMX transceiver designed to be easily attached to the back of an iPad. 
It allows DMX and ArtNet data to pass through the USB-C port.  


The device features a second USB-C port for charging the iPad while in use, as well as an additional USB-C port to connect various peripherals, such as your favorite MIDI faders. 
It is compatible with all apps that support ArtNet, including Blackout, Luminair, and others, as it creates a local Ethernet network.

This project is a collaboration with <PERSON><PERSON>, who make the core of the project: [USB_DMX](https://github.com/CShark/usb_dmx)

The Steady Node will be available for purchase soon.  
You can follow the open development process here and feel free to contribute or ask questions.
It's still in Beta but you are free to make and use this project. 
However, it is protected by a NC license, so commercial resale is not permitted.  

  

## Features


-Two LumenRadio TimoTwo CRMX modules

-Supports linking keys via the Bluetooth CRMX Toolbox app

-CRMX A: TX only

-CRMX B: TX and RX (can be used as an input)

-CRMX B can be disabled to save power

-Functions as an Ethernet port using the CDC NCM protocol, supported by iPadOS, iOS, macOS, Linux, Windows 11, and Windows 10 (with manual setup)

-Built-in DHCP server for plug-and-play IP configuration

-Supports DHCP client or fixed IP settings

-Due to an NDA, we cannot disclose the actual charger controller chip used in the device. Instead, we have substituted it with the LDR6023, which performs adequately.

## Schematic & PCB
You can find the hardware in [EasyEDA Pro](https://pro.easyeda.com/editor#id=aa68fd795ce04e85a0e2b847e1869c88,tab=9d22ed2ecd6648739174237369c177a5@aa68fd795ce04e85a0e2b847e1869c88|*4bbae9405bfa407bb26f5825b3228f58@aa68fd795ce04e85a0e2b847e1869c88)
## Firmware
The firmware is completely custom, only based on CMSIS (no HAL) and supports the important parts of the ArtNET protocol. Which port is an input and which an output can be set using the dip switches, but later be overwritten using either ArtNET commands, the WebUI or the display menu. The device also has a reset button to reset the configuration for when things go completely wrong. If the device is completely bricked it can be put into bootloader mode using a jumper on the mainboard.


The device supports mDNS and is always reachable using `artnet.local` in addition to its ip-address. The WebUI allows configuring each individual port with all the settings that ArtNET-Commands also support. You can also change the ip-configuration of the device and its internal minimal DHCP-Server, as well as clearing all settings, rebooting the device and switching the device into DFU/Bootloader mode to flash a new firmware.
![](/Images/webconfig1.png)
