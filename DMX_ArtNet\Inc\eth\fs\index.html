<html>

<head>
	<title>Steady Node</title>
	<link rel="stylesheet" type="text/css" href="style.css" />
	<script>
		window.onload = async () => {
			for (i = 0; i < 2; i++) {
				var result = await fetch("/config" + i);
				var txt = await result.text();
				var obj = txt.split('\n').reduce(function (result, item) { result[item.split(':')[0]] = item.split(':')[1]; return result; }, {});

				for (const [key, value] of Object.entries(obj)) {
					var elem = document.getElementsByName(key + i)[0];
					if(elem){
						if (elem.type == 'checkbox') {
							elem.checked = value == "1" ? 1 : 0;
						} else if (elem.type == 'radio') {
							elem = document.querySelector(`input[name='${key + i}'][value='${value}']`);
							elem.checked = 1;
						} else {
							elem.value = value;
						}
					}
				}
			}
		}
	</script>
</head>

<body>
	<h1><i>Steady Node</i></h1>

	<div>
		<nav>
			<a href="/">Port Config</a>
			<a href="/device.html">Device Config</a>
		</nav>

		<form class="container" action="/set-config" method="post">
			<div id="port1">
				<h3>CRMX A</h3>

				<div class="auto-grid"></div>
				
				<div class="auto-grid">
					Transmitter
					<label class="slider"><input type="checkbox" name="output0" value="1"><span></span></label>
					Receiver
				</div>

				<label>Universe <input type="number" min="0" max="32767" name="universe0"></label>

				<label>Name <input type="text" maxlength="18" name="name0"></label>

				<label>Description <textarea rows="4" maxlength="64" name="description0"></textarea></label>

				<div class="auto-grid">
					Continuous
					<label class="slider"><input type="checkbox" name="delta0" value="1"><span></span></label>
					Delta
				</div>

				<fieldset>
					<legend>Failover Mode</legend>

					<label class="radio"><input type="radio" value="0" name="failover0"> Hold</label>
					<label class="radio"><input type="radio" value="1" name="failover0"> Zero</label>
					<label class="radio"><input type="radio" value="2" name="failover0"> Full</label>
				</fieldset>
			</div>

			<div id="port2">
				<h3>CRMX B</h3>

				<div class="auto-grid">
					Power Off
					<label class="slider"><input type="checkbox" name="enable1" value="1"><span></span></label>
					Power On
				</div>

				<div class="auto-grid">
					Transmitter
					<label class="slider"><input type="checkbox" name="output1" value="1"><span></span></label>
					Receiver
				</div>
	
				<label>Universe <input type="number" min="0" max="32767" name="universe1"></label>

				<label>Name <input type="text" maxlength="18" name="name1"></label>

				<label>Description <textarea rows="4" maxlength="64" name="description1"></textarea></label>

				<div class="auto-grid">
					Continuous
					<label class="slider"><input type="checkbox" name="delta1" value="1"><span></span></label>
					Delta
				</div>

				<fieldset>
					<legend>Failover Mode</legend>

					<label class="radio"><input type="radio" value="0" name="failover1"> Hold</label>
					<label class="radio"><input type="radio" value="1" name="failover1"> Zero</label>
					<label class="radio"><input type="radio" value="2" name="failover1"> Full</label>
				</fieldset>
			</div>

			<input type="submit" value="Apply Settings">
		</form>
	</div>
</body>

</html>