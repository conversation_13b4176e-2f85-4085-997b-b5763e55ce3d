<title>Steady Node</title><link href=style.css rel=stylesheet><script>window.onload=async()=>{for(r of document.querySelectorAll("input[type='ip']"))r.type="text",r.minLength=7,r.maxLength=15,r.size=15,r.pattern="((^|\\.)((25[0-5])|(2[0-4]\\d)|(1\\d\\d)|([1-9]?\\d))){4}$",r.title="IP Address",r.required=!0;var e=await fetch("/ipconfig"),t=await e.arrayBuffer(),n=new TextDecoder("iso-8859-1").decode(t).split("\n").reduce((function(e,t){return e[t.split(":")[0]]=t.split(":")[1],e}),{});for(const[e,t]of Object.entries(n)){var r=document.getElementsByName(e)[0];"checkbox"==r.type?r.checked="1"==t?1:0:"radio"==r.type?(r=document.querySelector(`input[name='${e}'][value='${t}']`)).checked=1:r.value=t}};</script><h1><i>Steady Node</i></h1><div><nav><a href=/ >Port Config</a> <a href=/device.html>Device Config</a></nav><form action=/ip-config method=post class=container><h3 style="grid-column:1/span 4">Device</h3><div><fieldset><legend>IP Mode</legend><label class=radio><input name=ipmode type=radio value=0> Auto IP</label> <label class=radio><input name=ipmode type=radio value=1> Static</label> <label class=radio><input name=ipmode type=radio value=2> DHCP</label></fieldset><fieldset><legend>Static Settings</legend><label>IP<input name=s_ip type=ip></label> <label>Netmask<input name=s_mask type=ip></label> <label>Gateway<input name=s_gw type=ip></label></fieldset><fieldset><legend>DHCP Server</legend><div class=auto-grid>Enable DHCP Server <label class=slider><input name=dhcp_en type=checkbox><span></span></label></div><label>Device IP<input name=d_dev type=ip></label> <label>Host IP<input name=d_host type=ip></label> <label>Netmask<input name=d_mask type=ip></label></fieldset></div><div><fieldset><legend>Active IP Config</legend><label>mDNS Name<input value=artnet.local readonly></label> <label>IP<input name=a_ip readonly></label> <label>Netmask<input name=a_mask readonly></label> <label>Gateway<input name=a_gw readonly></label></fieldset><fieldset><legend>Firmware</legend><label>Version<input name=f_v readonly></label> <label>ArtNET-ID<input name=f_id readonly></label></fieldset><input type=submit value="Apply Settings"id=submit><div style=height:40px></div><input type=submit value="Reset Config"form=rstCfg> <input type=submit value="Reboot Device"form=rstDev> <input type=submit value="Reboot into DFU Mode"form=rstDfu></div></form></div><form action=/reset-config method=post hidden id=rstCfg></form><form action=/reset-device method=post hidden id=rstDev></form><form action=/reset-dfu method=post hidden id=rstDfu></form>