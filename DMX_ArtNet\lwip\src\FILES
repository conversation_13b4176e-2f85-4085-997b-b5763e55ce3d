api/      - The code for the high-level wrapper API. Not needed if
            you use the lowel-level call-back/raw API.

apps/     - Higher layer applications that are specifically programmed
            with the lwIP low-level raw API.

core/     - The core of the TPC/IP stack; protocol implementations,
            memory and buffer management, and the low-level raw API.

include/  - lwIP include files.

netif/    - Generic network interface device drivers are kept here.

For more information on the various subdirectories, check the FILES
file in each directory.
