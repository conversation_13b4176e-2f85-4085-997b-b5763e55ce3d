(kicad_sch (version 20230121) (generator eeschema)

  (uuid 9f67bc81-d3bc-45a8-a896-4f7d3309896c)

  (paper "A5")

  (lib_symbols
    (symbol "Connector:Conn_01x04_Pin" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x04_Pin" (at 0 -7.62 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_locked" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_keywords" "connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x04, script generated" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x04_Pin_1_1"
        (polyline
          (pts
            (xy 1.27 -5.08)
            (xy 0.8636 -5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 0.8636 -2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0)
            (xy 0.8636 0)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 2.54)
            (xy 0.8636 2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (rectangle (start 0.8636 -4.953) (end 0 -5.207)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -2.413) (end 0 -2.667)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 2.667) (end 0 2.413)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (pin passive line (at 5.08 2.54 180) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -2.54 180) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -5.08 180) (length 3.81)
          (name "Pin_4" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Connector:Conn_01x10_Pin" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 12.7 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x10_Pin" (at 0 -15.24 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_locked" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_keywords" "connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x10, script generated" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x10_Pin_1_1"
        (polyline
          (pts
            (xy 1.27 -12.7)
            (xy 0.8636 -12.7)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -10.16)
            (xy 0.8636 -10.16)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -7.62)
            (xy 0.8636 -7.62)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -5.08)
            (xy 0.8636 -5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 0.8636 -2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0)
            (xy 0.8636 0)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 2.54)
            (xy 0.8636 2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 5.08)
            (xy 0.8636 5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 7.62)
            (xy 0.8636 7.62)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 10.16)
            (xy 0.8636 10.16)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (rectangle (start 0.8636 -12.573) (end 0 -12.827)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -10.033) (end 0 -10.287)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -7.493) (end 0 -7.747)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -4.953) (end 0 -5.207)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -2.413) (end 0 -2.667)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 2.667) (end 0 2.413)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 5.207) (end 0 4.953)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 7.747) (end 0 7.493)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 10.287) (end 0 10.033)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (pin passive line (at 5.08 10.16 180) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -12.7 180) (length 3.81)
          (name "Pin_10" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 7.62 180) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 5.08 180) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 2.54 180) (length 3.81)
          (name "Pin_4" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 3.81)
          (name "Pin_5" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -2.54 180) (length 3.81)
          (name "Pin_6" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -5.08 180) (length 3.81)
          (name "Pin_7" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -7.62 180) (length 3.81)
          (name "Pin_8" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -10.16 180) (length 3.81)
          (name "Pin_9" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Jumper:SolderJumper_3_Open" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "JP" (at -2.54 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "SolderJumper_3_Open" (at 0 2.794 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "Solder Jumper SPDT" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Solder Jumper, 3-pole, open" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SolderJumper*Open*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "SolderJumper_3_Open_0_1"
        (arc (start -1.016 1.016) (mid -2.0276 0) (end -1.016 -1.016)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start -1.016 1.016) (mid -2.0276 0) (end -1.016 -1.016)
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (rectangle (start -0.508 1.016) (end 0.508 -1.016)
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (polyline
          (pts
            (xy -2.54 0)
            (xy -2.032 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.016 1.016)
            (xy -1.016 -1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -1.27)
            (xy 0 -1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.016 1.016)
            (xy 1.016 -1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 0)
            (xy 2.032 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 1.016 -1.016) (mid 2.0276 0) (end 1.016 1.016)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 1.016 -1.016) (mid 2.0276 0) (end 1.016 1.016)
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "SolderJumper_3_Open_1_1"
        (pin passive line (at -5.08 0 0) (length 2.54)
          (name "A" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.54)
          (name "C" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 2.54)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Switch:SW_Push" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "SW" (at 1.27 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "SW_Push" (at 0 -1.524 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "switch normally-open pushbutton push-button" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Push button switch, generic, two pins" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "SW_Push_0_1"
        (circle (center -2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 3.048)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 1.27)
            (xy -2.54 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (pin passive line (at -5.08 0 0) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 2.54)
          (name "2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:+3.3V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "+3.3V" (at 0 3.556 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"+3.3V\"" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "+3.3V_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "+3.3V_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "+3.3V" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 33.02 50.8) (diameter 0) (color 0 0 0 0)
    (uuid 01ca7311-59dc-4950-8a01-d69e5bfb9ff2)
  )
  (junction (at 69.85 54.61) (diameter 0) (color 0 0 0 0)
    (uuid 2b86e308-5e7c-4a15-9798-9820347e6948)
  )
  (junction (at 40.64 29.21) (diameter 0) (color 0 0 0 0)
    (uuid 400dd059-35e8-476d-a26a-eaa4b981901f)
  )
  (junction (at 40.64 19.05) (diameter 0) (color 0 0 0 0)
    (uuid 40dc51c8-bf86-4274-8891-226edd7f9ec6)
  )
  (junction (at 34.29 43.18) (diameter 0) (color 0 0 0 0)
    (uuid 7308bdd7-4602-4468-bcb5-f3dc6a9deabc)
  )
  (junction (at 69.85 43.18) (diameter 0) (color 0 0 0 0)
    (uuid 7612a81a-85a4-4c81-9fec-87d63458972b)
  )
  (junction (at 49.53 55.88) (diameter 0) (color 0 0 0 0)
    (uuid 7dfe1fea-a965-4d41-8132-c4ff0e54ec07)
  )
  (junction (at 46.99 50.8) (diameter 0) (color 0 0 0 0)
    (uuid 8413ac39-60e3-44bd-9cd8-08ee4f1e5c49)
  )
  (junction (at 46.99 58.42) (diameter 0) (color 0 0 0 0)
    (uuid 9cc6dd8c-e8f7-4f30-9282-8a7b16ab0458)
  )
  (junction (at 69.85 66.04) (diameter 0) (color 0 0 0 0)
    (uuid c0031157-7001-43ab-8dda-b973d31ebc70)
  )
  (junction (at 69.85 19.05) (diameter 0) (color 0 0 0 0)
    (uuid c9d12b2d-4d41-4943-8bdd-14f326663712)
  )
  (junction (at 46.99 43.18) (diameter 0) (color 0 0 0 0)
    (uuid e28fe788-eff9-4cd5-b40c-8f7af6921b26)
  )
  (junction (at 69.85 34.29) (diameter 0) (color 0 0 0 0)
    (uuid f974f391-8d9b-4cfc-95b4-0c3500011b0b)
  )

  (wire (pts (xy 31.75 63.5) (xy 52.07 63.5))
    (stroke (width 0) (type default))
    (uuid 00db8f60-8915-4a12-aac3-7db96c4f330e)
  )
  (wire (pts (xy 44.45 24.13) (xy 44.45 36.83))
    (stroke (width 0) (type default))
    (uuid 04193a3e-1239-4f3d-9519-93cf1dbf22f5)
  )
  (wire (pts (xy 49.53 29.21) (xy 49.53 55.88))
    (stroke (width 0) (type default))
    (uuid 087ce426-38fd-4520-b690-1a18b6e7172b)
  )
  (wire (pts (xy 31.75 50.8) (xy 33.02 50.8))
    (stroke (width 0) (type default))
    (uuid 14c7d79f-f65a-4caa-bded-fd3c0290549f)
  )
  (wire (pts (xy 33.02 41.91) (xy 31.75 41.91))
    (stroke (width 0) (type default))
    (uuid 16e6147f-79c0-4d10-8cd4-1a3823411599)
  )
  (wire (pts (xy 69.85 34.29) (xy 69.85 43.18))
    (stroke (width 0) (type default))
    (uuid 25bf60d2-e7ff-4a59-a834-103dd8879110)
  )
  (wire (pts (xy 40.64 29.21) (xy 49.53 29.21))
    (stroke (width 0) (type default))
    (uuid 3fd1f30a-a456-4509-8303-3d7ca952413f)
  )
  (wire (pts (xy 44.45 36.83) (xy 31.75 36.83))
    (stroke (width 0) (type default))
    (uuid 4ecb22fa-17e1-4f77-a359-120fac18cada)
  )
  (wire (pts (xy 44.45 43.18) (xy 46.99 43.18))
    (stroke (width 0) (type default))
    (uuid 526d38f2-5640-4d61-baca-4e606208304f)
  )
  (wire (pts (xy 67.31 77.47) (xy 69.85 77.47))
    (stroke (width 0) (type default))
    (uuid 52ddd423-7cb1-4ebb-8c32-8b2bc3426faf)
  )
  (wire (pts (xy 46.99 43.18) (xy 46.99 50.8))
    (stroke (width 0) (type default))
    (uuid 558054a9-ce42-4d35-a3cd-7bde01f776ba)
  )
  (wire (pts (xy 33.02 34.29) (xy 31.75 34.29))
    (stroke (width 0) (type default))
    (uuid 55b28e74-73ce-4091-af4a-2fd1d499dfd3)
  )
  (wire (pts (xy 46.99 34.29) (xy 69.85 34.29))
    (stroke (width 0) (type default))
    (uuid 56fbbe04-a3ec-4a39-86fc-c3eb4a222f21)
  )
  (wire (pts (xy 31.75 68.58) (xy 53.34 68.58))
    (stroke (width 0) (type default))
    (uuid 5d492b94-c69e-4d6e-bc8e-6b780f53457b)
  )
  (wire (pts (xy 46.99 58.42) (xy 46.99 71.12))
    (stroke (width 0) (type default))
    (uuid 72967c7e-8d6d-424d-bb68-90e451e25358)
  )
  (wire (pts (xy 52.07 63.5) (xy 52.07 54.61))
    (stroke (width 0) (type default))
    (uuid 81ebaf82-3563-4187-be85-02cf1c4e7733)
  )
  (wire (pts (xy 34.29 53.34) (xy 34.29 43.18))
    (stroke (width 0) (type default))
    (uuid 8722adf1-db3c-4f29-b7b2-969b53146344)
  )
  (wire (pts (xy 34.29 43.18) (xy 36.83 43.18))
    (stroke (width 0) (type default))
    (uuid 89f2133e-f3e2-41ab-a6d7-500a6d6edba6)
  )
  (wire (pts (xy 46.99 34.29) (xy 46.99 43.18))
    (stroke (width 0) (type default))
    (uuid 916bc34e-337c-4a88-a939-13b1d7774b3f)
  )
  (wire (pts (xy 69.85 66.04) (xy 69.85 77.47))
    (stroke (width 0) (type default))
    (uuid 91c6b188-c947-44bb-81ce-2d90cf73deea)
  )
  (wire (pts (xy 33.02 50.8) (xy 33.02 41.91))
    (stroke (width 0) (type default))
    (uuid 96c99fbc-0e58-4fbf-bfc1-cc6bfa52c247)
  )
  (wire (pts (xy 50.8 60.96) (xy 50.8 43.18))
    (stroke (width 0) (type default))
    (uuid 9bb66456-a451-454e-8dc5-d24d7f71037b)
  )
  (wire (pts (xy 49.53 55.88) (xy 49.53 81.28))
    (stroke (width 0) (type default))
    (uuid a25caa0b-54ff-4e54-be75-7eecf0020aee)
  )
  (wire (pts (xy 67.31 66.04) (xy 69.85 66.04))
    (stroke (width 0) (type default))
    (uuid a2b62015-ea1a-4b89-ae6d-02a56305247e)
  )
  (wire (pts (xy 52.07 54.61) (xy 57.15 54.61))
    (stroke (width 0) (type default))
    (uuid a8beae78-8630-41a3-8d5f-969850adc444)
  )
  (wire (pts (xy 69.85 19.05) (xy 69.85 34.29))
    (stroke (width 0) (type default))
    (uuid a96fd0f2-f469-42e3-8896-c2b1cc2dfe14)
  )
  (wire (pts (xy 69.85 54.61) (xy 69.85 66.04))
    (stroke (width 0) (type default))
    (uuid aaf6c72d-18c5-4466-9262-f8571ca1d47f)
  )
  (wire (pts (xy 33.02 24.13) (xy 33.02 34.29))
    (stroke (width 0) (type default))
    (uuid b03c5a55-dbfe-4d71-8666-ab7db7912922)
  )
  (wire (pts (xy 31.75 66.04) (xy 57.15 66.04))
    (stroke (width 0) (type default))
    (uuid b412f39c-88df-409c-93b3-039018812ec7)
  )
  (wire (pts (xy 44.45 50.8) (xy 46.99 50.8))
    (stroke (width 0) (type default))
    (uuid c9ab79d9-4bf5-467d-89b1-a1543e9522ec)
  )
  (wire (pts (xy 67.31 43.18) (xy 69.85 43.18))
    (stroke (width 0) (type default))
    (uuid cc8d8910-6013-44a8-a6e6-518a675d11ca)
  )
  (wire (pts (xy 31.75 55.88) (xy 49.53 55.88))
    (stroke (width 0) (type default))
    (uuid ccef8c64-9705-4cbc-b6ee-************)
  )
  (wire (pts (xy 31.75 71.12) (xy 46.99 71.12))
    (stroke (width 0) (type default))
    (uuid cf8a80cf-2870-46e6-bb65-294a09607945)
  )
  (wire (pts (xy 31.75 53.34) (xy 34.29 53.34))
    (stroke (width 0) (type default))
    (uuid cfb6728f-8fba-4a1f-9a71-b33aa9ab0b2b)
  )
  (wire (pts (xy 34.29 39.37) (xy 31.75 39.37))
    (stroke (width 0) (type default))
    (uuid d00af94d-27a2-45dc-bbc8-755752057dad)
  )
  (wire (pts (xy 31.75 58.42) (xy 46.99 58.42))
    (stroke (width 0) (type default))
    (uuid d0d6e236-cd2a-4c6b-8adb-09e2dba06526)
  )
  (wire (pts (xy 33.02 50.8) (xy 36.83 50.8))
    (stroke (width 0) (type default))
    (uuid d417bedb-d352-42d6-b622-970d5a67c6ef)
  )
  (wire (pts (xy 36.83 29.21) (xy 40.64 29.21))
    (stroke (width 0) (type default))
    (uuid db6c53a6-8a65-4f28-9a22-be41898ea15d)
  )
  (wire (pts (xy 40.64 19.05) (xy 69.85 19.05))
    (stroke (width 0) (type default))
    (uuid dbd6eea8-c207-405a-89eb-7f4e965fbbcf)
  )
  (wire (pts (xy 36.83 19.05) (xy 40.64 19.05))
    (stroke (width 0) (type default))
    (uuid df02354d-5035-4231-b8ff-322b5f5dd98c)
  )
  (wire (pts (xy 67.31 54.61) (xy 69.85 54.61))
    (stroke (width 0) (type default))
    (uuid e932c791-e5ac-4b9f-bd2f-fa3c054c4a9e)
  )
  (wire (pts (xy 53.34 68.58) (xy 53.34 77.47))
    (stroke (width 0) (type default))
    (uuid ea9d7eb9-8211-4f53-b1b8-e377055f10c9)
  )
  (wire (pts (xy 50.8 43.18) (xy 57.15 43.18))
    (stroke (width 0) (type default))
    (uuid eb596844-4e62-486f-aba2-2cc2907d4305)
  )
  (wire (pts (xy 31.75 60.96) (xy 50.8 60.96))
    (stroke (width 0) (type default))
    (uuid ebf4ebb3-6484-446d-919a-3574fb224f4d)
  )
  (wire (pts (xy 34.29 43.18) (xy 34.29 39.37))
    (stroke (width 0) (type default))
    (uuid f364499f-5e90-470f-8fbd-61210b2cafac)
  )
  (wire (pts (xy 46.99 50.8) (xy 46.99 58.42))
    (stroke (width 0) (type default))
    (uuid f8af92f5-2e6f-4bc9-bbbd-3278532e41cf)
  )
  (wire (pts (xy 53.34 77.47) (xy 57.15 77.47))
    (stroke (width 0) (type default))
    (uuid fc9f4658-7212-4f84-9f67-dc37a27fccb7)
  )
  (wire (pts (xy 69.85 43.18) (xy 69.85 54.61))
    (stroke (width 0) (type default))
    (uuid ff704e1b-7bdc-4a6e-aa78-bc3379739f92)
  )

  (label "SCL" (at 33.02 39.37 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 22180a5f-a63e-4b91-9842-f5875b1bae16)
  )
  (label "GND" (at 49.53 29.21 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 44b27dca-8803-4a2b-88d5-8f23beb80ac1)
  )
  (label "SDA" (at 33.02 41.91 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 94fdf9a5-b189-499d-8ad1-7d8efb074804)
  )
  (label "VCC" (at 53.34 19.05 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid aaf811ec-3fdb-4fe9-a310-fe9c85788912)
  )

  (symbol (lib_id "Switch:SW_Push") (at 62.23 43.18 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 1a011ca1-7233-4a4c-b58a-ec0d89d2a5a4)
    (property "Reference" "SW1" (at 62.23 34.7091 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Back" (at 62.23 36.6301 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_PUSH_6mm_H8.5mm" (at 62.23 38.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 62.23 38.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 910d2ddf-a809-4f62-a425-6f93f24f922b))
    (pin "2" (uuid 9add4307-385a-433d-a624-bfeec2ee842c))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "SW1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 40.64 43.18 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 23786c64-3489-45df-9b03-17a80050ce38)
    (property "Reference" "R1" (at 40.64 39.1541 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "4K7" (at 40.64 41.0751 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 40.64 44.958 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 40.64 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 896b895d-95c5-4a2c-888b-1ba0ff702e22))
    (pin "2" (uuid c42d13e1-8a6b-4bf9-8f68-486673d2c820))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "R1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Conn_01x10_Pin") (at 26.67 60.96 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 2a156e28-1f01-458b-a58f-f3d2a4352dcc)
    (property "Reference" "J2" (at 27.305 47.7139 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Connector" (at 27.305 49.6349 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Connector_FFC-FPC:TE_1-84952-0_1x10-1MP_P1.0mm_Horizontal" (at 26.67 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 26.67 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 97d5dbfd-f5cd-460f-8e07-f8eb81826a92))
    (pin "10" (uuid cd9e6921-df84-4e96-af7e-1c3bdeff1680))
    (pin "2" (uuid 182aa876-d203-4da1-8245-4cf933ebd273))
    (pin "3" (uuid 4ffad099-fbf4-4554-abdf-8b0f20f8fd13))
    (pin "4" (uuid abab534b-2f2c-4ca3-8ce1-b6349a4e80b5))
    (pin "5" (uuid 868255fa-a236-4e72-84d3-8ca574661dbc))
    (pin "6" (uuid be771895-e40c-42e3-b050-98c7879da996))
    (pin "7" (uuid f95719d3-bcc6-4766-83fc-5152544b3ce9))
    (pin "8" (uuid a780d7db-c6e7-468c-9b83-dbd3f03c0946))
    (pin "9" (uuid ddd77815-5dd8-411c-9bd5-84c06e8434c9))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "J2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Jumper:SolderJumper_3_Open") (at 40.64 24.13 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 2d847454-86c1-4e17-8955-b1fc2411dda5)
    (property "Reference" "JP2" (at 48.26 26.67 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "PIN 2" (at 49.53 21.59 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Jumper:SolderJumper-3_P2.0mm_Open_TrianglePad1.0x1.5mm" (at 40.64 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 40.64 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 63721e6a-22df-4677-b8f4-ed76d02bcef4))
    (pin "2" (uuid 3261ae4e-6335-486b-a67d-66db305d23f7))
    (pin "3" (uuid 88d9ce84-ceda-48db-920e-7937bf5a27f6))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "JP2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 49.53 81.28 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 8a2ba2cb-b674-4083-8c62-19df4a6039db)
    (property "Reference" "#PWR02" (at 49.53 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 49.53 85.4155 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 49.53 81.28 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 49.53 81.28 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 82b9c858-6722-4d78-95b7-cac3becb0cf3))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "#PWR02") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_Push") (at 62.23 54.61 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 8e27603f-08f9-4a80-a479-0ec4540de0f6)
    (property "Reference" "SW4" (at 62.23 46.1391 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Confirm" (at 62.23 48.0601 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_PUSH_6mm_H8.5mm" (at 62.23 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 62.23 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid af3d2700-fe13-4de8-841e-fba889dbef4e))
    (pin "2" (uuid b03d6bd3-1076-4338-aeb1-a6857ad12ec8))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "SW4") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 69.85 19.05 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid a3084707-dc61-428c-b61e-30057bd8904e)
    (property "Reference" "#PWR01" (at 69.85 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 69.85 15.5481 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 69.85 19.05 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 69.85 19.05 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 30b37532-5ee7-4a47-9331-7cc67f5d5299))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "#PWR01") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_Push") (at 62.23 66.04 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid af038ba5-c8d9-4e4b-ac5b-642d6c7155d8)
    (property "Reference" "SW2" (at 62.23 57.5691 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Up" (at 62.23 59.4901 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_PUSH_6mm_H8.5mm" (at 62.23 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 62.23 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6a7d78a2-2e8d-4d9c-a9df-f2a766afb1c4))
    (pin "2" (uuid ab0eb873-45d3-4446-8d19-0d2d9ddf2f56))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "SW2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Jumper:SolderJumper_3_Open") (at 36.83 24.13 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid b3238d02-9428-4d24-a990-6051a0e9bdd3)
    (property "Reference" "JP1" (at 29.21 26.67 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "PIN 1" (at 29.21 21.59 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Jumper:SolderJumper-3_P2.0mm_Open_TrianglePad1.0x1.5mm" (at 36.83 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 36.83 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b4ad9419-2416-4bec-a3cf-123a973226ab))
    (pin "2" (uuid 9825fc0d-a62a-47d5-adb2-1b54d90ac65c))
    (pin "3" (uuid 45d431dd-1771-4fb7-a18b-c425d6a0a2d5))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "JP1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Conn_01x04_Pin") (at 26.67 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid bb52d49e-060e-4e5c-b3b3-cf880a7397a2)
    (property "Reference" "J1" (at 27.305 31.2039 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "OLED" (at 27.305 33.1249 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x04_P2.54mm_Vertical" (at 26.67 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 26.67 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1014c170-4889-40c9-affc-a47adf84afff))
    (pin "2" (uuid 46e6f7e1-bb42-4f28-8174-12ed0f500306))
    (pin "3" (uuid 4d64b359-7c94-4d05-a3c9-cfa5e07e03a8))
    (pin "4" (uuid 0bfd0abe-846f-448c-8815-6f91f1986c16))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "J1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_Push") (at 62.23 77.47 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid c162340e-07e6-42cf-863c-9b3a53b5ccc1)
    (property "Reference" "SW3" (at 62.23 68.9991 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Down" (at 62.23 70.9201 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_PUSH_6mm_H8.5mm" (at 62.23 72.39 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 62.23 72.39 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9827c1ee-a587-4a23-bc78-3693b7796067))
    (pin "2" (uuid b46524f2-d76f-4468-bde6-7f488d3ab82d))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "SW3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 40.64 50.8 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid fd5d5835-2160-44f8-bb83-cf2b2e44657a)
    (property "Reference" "R2" (at 40.64 46.7741 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "4K7" (at 40.64 48.6951 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 40.64 52.578 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 40.64 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 449b26db-cd16-40f7-88b2-250cdd77ee6c))
    (pin "2" (uuid 4212a869-2de2-467d-8fd1-f550e630fe58))
    (instances
      (project "DMX Subboard"
        (path "/9f67bc81-d3bc-45a8-a896-4f7d3309896c"
          (reference "R2") (unit 1)
        )
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
