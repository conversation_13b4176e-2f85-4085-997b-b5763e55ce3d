<html>

<head>
    <title>Steady Node</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <script>
        window.onload = async () => {
            for (elem of document.querySelectorAll("input[type='ip']")) {
                elem.type = "text";
                elem.minLength = 7;
                elem.maxLength = 15;
                elem.size = 15;
                elem.pattern = "((^|\\.)((25[0-5])|(2[0-4]\\d)|(1\\d\\d)|([1-9]?\\d))){4}$";
                elem.title = "IP Address";
                elem.required = true;
            }

            var result = await fetch("/ipconfig");
            var buf = await result.arrayBuffer();
            var decoder = new TextDecoder('iso-8859-1');
            var txt = decoder.decode(buf);
            var obj = txt.split('\n').reduce(function (result, item) { result[item.split(':')[0]] = item.split(':')[1]; return result; }, {});

            for (const [key, value] of Object.entries(obj)) {
                var elem = document.getElementsByName(key)[0];
                if (elem && elem.type == 'checkbox') {
                    elem.checked = value == "1" ? 1 : 0;
                } else if (elem && elem.type == 'radio') {
                    elem = document.querySelector(`input[name='${key}'][value='${value}']`);
                    if (elem) elem.checked = 1;
                } else if (elem) {
                    elem.value = value;
                }
            }

            // Unified IP Settings: Sync visible fields with hidden backend fields
            function syncUnifiedFields() {
                // Sync Steady Node IP: d_dev (visible) -> s_ip (hidden)
                var steadyNodeIP = document.getElementsByName('d_dev')[0];
                var staticIP = document.getElementsByName('s_ip')[0];
                if (steadyNodeIP && staticIP) {
                    staticIP.value = steadyNodeIP.value;
                }

                // Sync Net Mask: d_mask (visible) -> s_mask (hidden)
                var netMask = document.getElementsByName('d_mask')[0];
                var staticMask = document.getElementsByName('s_mask')[0];
                if (netMask && staticMask) {
                    staticMask.value = netMask.value;
                }

                // Auto-manage DHCP Server enable based on IP mode
                var ipMode = document.querySelector('input[name="ipmode"]:checked');
                var dhcpEnable = document.getElementsByName('dhcp_en')[0];
                if (ipMode && dhcpEnable) {
                    dhcpEnable.checked = ipMode.value == "0"; // Enable for DHCP Server mode
                }
            }

            // Add event listeners for real-time synchronization
            document.addEventListener('change', syncUnifiedFields);
            document.addEventListener('input', syncUnifiedFields);

            // Initial sync
            syncUnifiedFields();
        }
    </script>
</head>

<body>
    <h1><i>Steady Node</i></h1>

    <div>
        <nav>
            <a href="/">Port Config</a>
            <a href="/device.html">Device Config</a>
        </nav>

        <form class="container" action="/ip-config" method="post">
            <h3 style="grid-column: 1 / span 4">Device</h3>
            <div>
                <fieldset>
                    <legend>IP Mode</legend>

                    <label class="radio"><input type="radio" value="0" name="ipmode"> DHCP Server (Default)</label>
                    <label class="radio"><input type="radio" value="1" name="ipmode"> Static IP</label>
                </fieldset>

                <!-- Unified IP Settings section - replaces previous Static Settings, DHCP Server sections -->
                <fieldset>
                    <legend>IP Settings</legend>

                    <!-- Hidden DHCP Server enable checkbox - automatically managed by IP mode selection -->
                    <input type="checkbox" name="dhcp_en" style="display: none;">

                    <!-- Steady Node IP - unified field for both DHCP Server (d_dev) and Static IP (s_ip) -->
                    <label>Steady Node IP<input type="ip" name="d_dev"></label>

                    <!-- Net Mask - unified field for both modes, maps to DHCP subnet (d_mask) -->
                    <label>Net Mask<input type="ip" name="d_mask"></label>

                    <!-- iPad IP - visible, Gateway hidden for simplification -->
                    <label>iPad IP<input type="ip" name="d_host"></label>

                    <!-- Gateway hidden but preserved for backend -->
                    <input type="ip" name="s_gw" style="display: none;">

                    <!-- Local DNS Name - mDNS hostname -->
                    <label>Local DNS URL Name<input type="text" name="dns_name" maxlength="32"></label>

                    <!-- Hidden static IP field - automatically synced with Steady Node IP -->
                    <input type="ip" name="s_ip" style="display: none;">
                    <!-- Hidden static netmask field - automatically synced with Net Mask -->
                    <input type="ip" name="s_mask" style="display: none;">
                </fieldset>
            </div>

            <div>
                <!-- Active IP Config section removed as requested - backend logic preserved -->

                <fieldset>
                    <legend>Firmware</legend>
                    <label>Version<input type="text" name="f_v" readonly></label>
                    <label>ArtNET-ID<input type="text" name="f_id" readonly></label>
                </fieldset>

                <input id="submit" type="submit" value="Apply Settings">
                <div style="height: 40px"></div>


                <input type="submit" value="Reset Config" form="rstCfg">
                <input type="submit" value="Reboot Device" form="rstDev">
                <input type="submit" value="Reboot into DFU Mode" form="rstDfu">
            </div>
        </form>
    </div>

    <form action="/reset-config" method="post" id="rstCfg" hidden></form>
    <form action="/reset-device" method="post" id="rstDev" hidden></form>
    <form action="/reset-dfu" method="post" id="rstDfu" hidden></form>
</body>

</html>