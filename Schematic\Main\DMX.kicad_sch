(kicad_sch (version 20230121) (generator eeschema)

  (uuid 535f7e00-920f-4f85-bbb1-ce11653908ad)

  (paper "A3")

  (lib_symbols
    (symbol "Connector:Conn_01x06_Male" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 7.62 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x06_Male" (at 0 -10.16 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x06, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x06_Male_1_1"
        (polyline
          (pts
            (xy 1.27 -7.62)
            (xy 0.8636 -7.62)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -5.08)
            (xy 0.8636 -5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 0.8636 -2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0)
            (xy 0.8636 0)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 2.54)
            (xy 0.8636 2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 5.08)
            (xy 0.8636 5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (rectangle (start 0.8636 -7.493) (end 0 -7.747)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -4.953) (end 0 -5.207)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -2.413) (end 0 -2.667)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 2.667) (end 0 2.413)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 5.207) (end 0 4.953)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (pin passive line (at 5.08 5.08 180) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 2.54 180) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -2.54 180) (length 3.81)
          (name "Pin_4" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -5.08 180) (length 3.81)
          (name "Pin_5" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -7.62 180) (length 3.81)
          (name "Pin_6" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Connector:Conn_01x10_Pin" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 12.7 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x10_Pin" (at 0 -15.24 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_locked" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_keywords" "connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x10, script generated" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x10_Pin_1_1"
        (polyline
          (pts
            (xy 1.27 -12.7)
            (xy 0.8636 -12.7)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -10.16)
            (xy 0.8636 -10.16)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -7.62)
            (xy 0.8636 -7.62)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -5.08)
            (xy 0.8636 -5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 0.8636 -2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0)
            (xy 0.8636 0)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 2.54)
            (xy 0.8636 2.54)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 5.08)
            (xy 0.8636 5.08)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 7.62)
            (xy 0.8636 7.62)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 10.16)
            (xy 0.8636 10.16)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (rectangle (start 0.8636 -12.573) (end 0 -12.827)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -10.033) (end 0 -10.287)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -7.493) (end 0 -7.747)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -4.953) (end 0 -5.207)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 -2.413) (end 0 -2.667)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 2.667) (end 0 2.413)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 5.207) (end 0 4.953)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 7.747) (end 0 7.493)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (rectangle (start 0.8636 10.287) (end 0 10.033)
          (stroke (width 0.1524) (type default))
          (fill (type outline))
        )
        (pin passive line (at 5.08 10.16 180) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -12.7 180) (length 3.81)
          (name "Pin_10" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 7.62 180) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 5.08 180) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 2.54 180) (length 3.81)
          (name "Pin_4" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 3.81)
          (name "Pin_5" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -2.54 180) (length 3.81)
          (name "Pin_6" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -5.08 180) (length 3.81)
          (name "Pin_7" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -7.62 180) (length 3.81)
          (name "Pin_8" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -10.16 180) (length 3.81)
          (name "Pin_9" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Connector:Screw_Terminal_01x03" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Screw_Terminal_01x03" (at 0 -5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "screw terminal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic screw terminal, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "TerminalBlock*:*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Screw_Terminal_01x03_1_1"
        (rectangle (start -1.27 3.81) (end 1.27 -3.81)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (circle (center 0 -2.54) (radius 0.635)
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.5334 -2.2098)
            (xy 0.3302 -3.048)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.5334 0.3302)
            (xy 0.3302 -0.508)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.5334 2.8702)
            (xy 0.3302 2.032)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.3556 -2.032)
            (xy 0.508 -2.8702)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.3556 0.508)
            (xy 0.508 -0.3302)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.3556 3.048)
            (xy 0.508 2.2098)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 0.635)
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (circle (center 0 2.54) (radius 0.635)
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (pin passive line (at -5.08 2.54 0) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -2.54 0) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Connector:USB_C_Receptacle_USB2.0" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at -10.16 19.05 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "USB_C_Receptacle_USB2.0" (at 19.05 19.05 0)
        (effects (font (size 1.27 1.27)) (justify right))
      )
      (property "Footprint" "" (at 3.81 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "https://www.usb.org/sites/default/files/documents/usb_type-c.zip" (at 3.81 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "usb universal serial bus type-C USB2.0" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "USB 2.0-only Type-C Receptacle connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "USB*C*Receptacle*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "USB_C_Receptacle_USB2.0_0_0"
        (rectangle (start -0.254 -17.78) (end 0.254 -16.764)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 -14.986) (end 9.144 -15.494)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 -12.446) (end 9.144 -12.954)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 -4.826) (end 9.144 -5.334)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 -2.286) (end 9.144 -2.794)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 0.254) (end 9.144 -0.254)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 2.794) (end 9.144 2.286)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 7.874) (end 9.144 7.366)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 10.414) (end 9.144 9.906)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (rectangle (start 10.16 15.494) (end 9.144 14.986)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "USB_C_Receptacle_USB2.0_0_1"
        (rectangle (start -10.16 17.78) (end 10.16 -17.78)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (arc (start -8.89 -3.81) (mid -6.985 -5.7067) (end -5.08 -3.81)
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (arc (start -7.62 -3.81) (mid -6.985 -4.4423) (end -6.35 -3.81)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (arc (start -7.62 -3.81) (mid -6.985 -4.4423) (end -6.35 -3.81)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (rectangle (start -7.62 -3.81) (end -6.35 3.81)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (arc (start -6.35 3.81) (mid -6.985 4.4423) (end -7.62 3.81)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (arc (start -6.35 3.81) (mid -6.985 4.4423) (end -7.62 3.81)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (arc (start -5.08 3.81) (mid -6.985 5.7067) (end -8.89 3.81)
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (circle (center -2.54 1.143) (radius 0.635)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (circle (center 0 -5.842) (radius 1.27)
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (polyline
          (pts
            (xy -8.89 -3.81)
            (xy -8.89 3.81)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -5.08 3.81)
            (xy -5.08 -3.81)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -5.842)
            (xy 0 4.318)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -3.302)
            (xy -2.54 -0.762)
            (xy -2.54 0.508)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -2.032)
            (xy 2.54 0.508)
            (xy 2.54 1.778)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 4.318)
            (xy 0 6.858)
            (xy 1.27 4.318)
            (xy -1.27 4.318)
          )
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (rectangle (start 1.905 1.778) (end 3.175 3.048)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
      )
      (symbol "USB_C_Receptacle_USB2.0_1_1"
        (pin passive line (at 0 -22.86 90) (length 5.08)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "A1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -22.86 90) (length 5.08) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "A12" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 15.24 15.24 180) (length 5.08)
          (name "VBUS" (effects (font (size 1.27 1.27))))
          (number "A4" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 10.16 180) (length 5.08)
          (name "CC1" (effects (font (size 1.27 1.27))))
          (number "A5" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 -2.54 180) (length 5.08)
          (name "D+" (effects (font (size 1.27 1.27))))
          (number "A6" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 2.54 180) (length 5.08)
          (name "D-" (effects (font (size 1.27 1.27))))
          (number "A7" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 -12.7 180) (length 5.08)
          (name "SBU1" (effects (font (size 1.27 1.27))))
          (number "A8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 15.24 15.24 180) (length 5.08) hide
          (name "VBUS" (effects (font (size 1.27 1.27))))
          (number "A9" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -22.86 90) (length 5.08) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "B1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -22.86 90) (length 5.08) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "B12" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 15.24 15.24 180) (length 5.08) hide
          (name "VBUS" (effects (font (size 1.27 1.27))))
          (number "B4" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 7.62 180) (length 5.08)
          (name "CC2" (effects (font (size 1.27 1.27))))
          (number "B5" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 -5.08 180) (length 5.08)
          (name "D+" (effects (font (size 1.27 1.27))))
          (number "B6" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 0 180) (length 5.08)
          (name "D-" (effects (font (size 1.27 1.27))))
          (number "B7" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 15.24 -15.24 180) (length 5.08)
          (name "SBU2" (effects (font (size 1.27 1.27))))
          (number "B8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 15.24 15.24 180) (length 5.08) hide
          (name "VBUS" (effects (font (size 1.27 1.27))))
          (number "B9" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -7.62 -22.86 90) (length 5.08)
          (name "SHIELD" (effects (font (size 1.27 1.27))))
          (number "S1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Connector_Generic:Conn_01x01" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x01" (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x01_1_1"
        (rectangle (start -1.27 0.127) (end 0 -0.127)
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (rectangle (start -1.27 1.27) (end 1.27 -1.27)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "CustomParts:STM32G441RB" (in_bom yes) (on_board yes)
      (property "Reference" "U" (at 19.685 -55.88 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "STM32G441RB" (at 19.685 -53.34 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "Package_QFP:LQFP-64_10x10mm_P0.5mm" (at 19.685 -55.88 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "https://cdn-reichelt.de/documents/datenblatt/A300/STM32G441XB.pdf" (at 19.685 -55.88 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "STM32G441RB_0_1"
        (rectangle (start -17.78 43.18) (end 17.78 -50.8)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "STM32G441RB_1_1"
        (pin power_in line (at -6.35 48.26 270) (length 5.08)
          (name "VBAT" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -7.62 0) (length 5.08)
          (name "PC2" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -10.16 0) (length 5.08)
          (name "PC3" (effects (font (size 1.27 1.27))))
          (number "11" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 40.64 180) (length 5.08)
          (name "PA0" (effects (font (size 1.27 1.27))))
          (number "12" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 38.1 180) (length 5.08)
          (name "PA1" (effects (font (size 1.27 1.27))))
          (number "13" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 35.56 180) (length 5.08)
          (name "PA2" (effects (font (size 1.27 1.27))))
          (number "14" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -5.08 -55.88 90) (length 5.08)
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "15" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -3.81 48.26 270) (length 5.08)
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "16" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 33.02 180) (length 5.08)
          (name "PA3" (effects (font (size 1.27 1.27))))
          (number "17" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 30.48 180) (length 5.08)
          (name "PA4" (effects (font (size 1.27 1.27))))
          (number "18" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 27.94 180) (length 5.08)
          (name "PA5" (effects (font (size 1.27 1.27))))
          (number "19" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -35.56 0) (length 5.08)
          (name "PC13" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 25.4 180) (length 5.08)
          (name "PA6" (effects (font (size 1.27 1.27))))
          (number "20" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 22.86 180) (length 5.08)
          (name "PA7" (effects (font (size 1.27 1.27))))
          (number "21" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -12.7 0) (length 5.08)
          (name "PC4" (effects (font (size 1.27 1.27))))
          (number "22" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -15.24 0) (length 5.08)
          (name "PC5" (effects (font (size 1.27 1.27))))
          (number "23" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -2.54 180) (length 5.08)
          (name "PB0" (effects (font (size 1.27 1.27))))
          (number "24" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -5.08 180) (length 5.08)
          (name "PB1" (effects (font (size 1.27 1.27))))
          (number "25" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -7.62 180) (length 5.08)
          (name "PB2" (effects (font (size 1.27 1.27))))
          (number "26" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 5.08 -55.88 90) (length 5.08)
          (name "VSSA" (effects (font (size 1.27 1.27))))
          (number "27" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -22.86 35.56 0) (length 5.08)
          (name "VREF+" (effects (font (size 1.27 1.27))))
          (number "28" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 6.35 48.26 270) (length 5.08)
          (name "VDDA" (effects (font (size 1.27 1.27))))
          (number "29" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -38.1 0) (length 5.08)
          (name "PC14" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -27.94 180) (length 5.08)
          (name "PB10" (effects (font (size 1.27 1.27))))
          (number "30" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 -55.88 90) (length 5.08)
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "31" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -1.27 48.26 270) (length 5.08)
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "32" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -30.48 180) (length 5.08)
          (name "PB11" (effects (font (size 1.27 1.27))))
          (number "33" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -33.02 180) (length 5.08)
          (name "PB12" (effects (font (size 1.27 1.27))))
          (number "34" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -35.56 180) (length 5.08)
          (name "PB13" (effects (font (size 1.27 1.27))))
          (number "35" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -38.1 180) (length 5.08)
          (name "PB14" (effects (font (size 1.27 1.27))))
          (number "36" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -40.64 180) (length 5.08)
          (name "PB15" (effects (font (size 1.27 1.27))))
          (number "37" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -17.78 0) (length 5.08)
          (name "PC6" (effects (font (size 1.27 1.27))))
          (number "38" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -20.32 0) (length 5.08)
          (name "PC7" (effects (font (size 1.27 1.27))))
          (number "39" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -40.64 0) (length 5.08)
          (name "PC15" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -22.86 0) (length 5.08)
          (name "PC8" (effects (font (size 1.27 1.27))))
          (number "40" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -25.4 0) (length 5.08)
          (name "PC9" (effects (font (size 1.27 1.27))))
          (number "41" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 20.32 180) (length 5.08)
          (name "PA8" (effects (font (size 1.27 1.27))))
          (number "42" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 17.78 180) (length 5.08)
          (name "PA9" (effects (font (size 1.27 1.27))))
          (number "43" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 15.24 180) (length 5.08)
          (name "PA10" (effects (font (size 1.27 1.27))))
          (number "44" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 12.7 180) (length 5.08)
          (name "PA11" (effects (font (size 1.27 1.27))))
          (number "45" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 10.16 180) (length 5.08)
          (name "PA12" (effects (font (size 1.27 1.27))))
          (number "46" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 -55.88 90) (length 5.08)
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "47" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 1.27 48.26 270) (length 5.08)
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "48" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 7.62 180) (length 5.08)
          (name "PA13" (effects (font (size 1.27 1.27))))
          (number "49" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -22.86 19.05 0) (length 5.08)
          (name "PF0" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 5.08 180) (length 5.08)
          (name "PA14" (effects (font (size 1.27 1.27))))
          (number "50" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 2.54 180) (length 5.08)
          (name "PA15" (effects (font (size 1.27 1.27))))
          (number "51" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -27.94 0) (length 5.08)
          (name "PC10" (effects (font (size 1.27 1.27))))
          (number "52" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -30.48 0) (length 5.08)
          (name "PC11" (effects (font (size 1.27 1.27))))
          (number "53" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -33.02 0) (length 5.08)
          (name "PC12" (effects (font (size 1.27 1.27))))
          (number "54" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 11.43 0) (length 5.08)
          (name "PD2" (effects (font (size 1.27 1.27))))
          (number "55" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -10.16 180) (length 5.08)
          (name "PB3" (effects (font (size 1.27 1.27))))
          (number "56" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -12.7 180) (length 5.08)
          (name "PB4" (effects (font (size 1.27 1.27))))
          (number "57" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -15.24 180) (length 5.08)
          (name "PB5" (effects (font (size 1.27 1.27))))
          (number "58" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -17.78 180) (length 5.08)
          (name "PB6" (effects (font (size 1.27 1.27))))
          (number "59" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -22.86 16.51 0) (length 5.08)
          (name "PF1" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -20.32 180) (length 5.08)
          (name "PB7" (effects (font (size 1.27 1.27))))
          (number "60" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -22.86 180) (length 5.08)
          (name "PB8_BOOT0" (effects (font (size 1.27 1.27))))
          (number "61" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 22.86 -25.4 180) (length 5.08)
          (name "PB9" (effects (font (size 1.27 1.27))))
          (number "62" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 2.54 -55.88 90) (length 5.08)
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "63" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 3.81 48.26 270) (length 5.08)
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "64" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -22.86 24.13 0) (length 5.08)
          (name "PG10_NRST" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -2.54 0) (length 5.08)
          (name "PC0" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at -22.86 -5.08 0) (length 5.08)
          (name "PC1" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "cap capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Unpolarized capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "C_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:Crystal" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "Y" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Crystal" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "quartz ceramic resonator oscillator" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Two pin crystal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Crystal*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Crystal_0_1"
        (rectangle (start -1.143 2.54) (end 1.143 -2.54)
          (stroke (width 0.3048) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.54 0)
            (xy -1.905 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 -1.27)
            (xy -1.905 1.27)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.905 -1.27)
            (xy 1.905 1.27)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 0)
            (xy 1.905 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "Crystal_1_1"
        (pin passive line (at -3.81 0 0) (length 1.27)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 3.81 0 180) (length 1.27)
          (name "2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:D_TVS" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "D" (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "D_TVS" (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "diode TVS thyrector" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Bidirectional transient-voltage-suppression diode" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "TO-???* *_Diode_* *SingleDiode* D_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "D_TVS_0_1"
        (polyline
          (pts
            (xy 1.27 0)
            (xy -1.27 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.508 1.27)
            (xy 0 1.27)
            (xy 0 -1.27)
            (xy -0.508 -1.27)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.54 1.27)
            (xy -2.54 -1.27)
            (xy 2.54 1.27)
            (xy 2.54 -1.27)
            (xy -2.54 1.27)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "D_TVS_1_1"
        (pin passive line (at -3.81 0 0) (length 2.54)
          (name "A1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 3.81 0 180) (length 2.54)
          (name "A2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "GND_1" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND_1" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_1_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Interface_UART:MAX481E" (in_bom yes) (on_board yes)
      (property "Reference" "U" (at -6.096 11.43 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "MAX481E" (at 0.762 11.43 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 -17.78 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf" (at 0 1.27 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Half duplex RS-485/RS-422, 2.5 Mbps, ±15kV electro-static discharge (ESD) protection, no slew-rate, with low-power shutdown, with receiver/driver enable, 32 receiver drive kapacitity, DIP-8 and SOIC-8" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "DIP*W7.62mm* SOIC*3.9x4.9mm*P1.27mm*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "MAX481E_0_1"
        (rectangle (start -7.62 10.16) (end 7.62 -12.7)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (circle (center -0.3048 -3.683) (radius 0.3556)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (circle (center -0.0254 1.4986) (radius 0.3556)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
        (polyline
          (pts
            (xy -4.064 -5.08)
            (xy -1.905 -5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.064 2.54)
            (xy -1.27 2.54)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 -3.2004)
            (xy -1.27 -3.4544)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.635 -5.08)
            (xy 5.334 -5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.064 -2.54)
            (xy -1.27 -2.54)
            (xy -1.27 -3.175)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 0)
            (xy -4.064 0)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 3.175)
            (xy 3.81 3.175)
            (xy 3.81 -5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 1.905)
            (xy 2.54 -3.81)
            (xy 0 -3.81)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 -3.175)
            (xy -1.905 -5.715)
            (xy 0.635 -4.445)
            (xy -1.905 -3.175)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 2.54)
            (xy 1.27 3.81)
            (xy 1.27 1.27)
            (xy -1.27 2.54)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.905 1.905)
            (xy 4.445 1.905)
            (xy 4.445 2.54)
            (xy 5.334 2.54)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (rectangle (start 1.27 3.175) (end 1.27 3.175)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.651 1.905) (radius 0.3556)
          (stroke (width 0.254) (type default))
          (fill (type outline))
        )
      )
      (symbol "MAX481E_1_1"
        (pin output line (at -10.16 2.54 0) (length 2.54)
          (name "RO" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -10.16 0 0) (length 2.54)
          (name "~{RE}" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -10.16 -2.54 0) (length 2.54)
          (name "DE" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -10.16 -5.08 0) (length 2.54)
          (name "DI" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 -15.24 90) (length 2.54)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 10.16 -5.08 180) (length 2.54)
          (name "A" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin bidirectional line (at 10.16 2.54 180) (length 2.54)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 12.7 270) (length 2.54)
          (name "VCC" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Jumper:Jumper_3_Bridged12" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "JP" (at -2.54 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Jumper_3_Bridged12" (at 0 2.794 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "Jumper SPDT" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Jumper, 3-pole, pins 1+2 closed/bridged" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Jumper* TestPoint*3Pads* TestPoint*Bridge*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Jumper_3_Bridged12_0_0"
        (circle (center -3.302 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 3.302 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "Jumper_3_Bridged12_0_1"
        (arc (start -0.254 0.508) (mid -1.651 0.9912) (end -3.048 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -1.27)
            (xy 0 -0.508)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "Jumper_3_Bridged12_1_1"
        (pin passive line (at -6.35 0 0) (length 2.54)
          (name "A" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 0 -3.81 90) (length 2.54)
          (name "C" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 6.35 0 180) (length 2.54)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Power_Protection:NUP4202" (in_bom yes) (on_board yes)
      (property "Reference" "U" (at -6.985 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "NUP4202" (at 6.35 1.27 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "Package_TO_SOT_SMD:SOT-363_SC-70-6" (at 1.27 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "http://www.onsemi.com/pub_link/Collateral/NUP4202W1-D.PDF" (at 1.27 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "ESD Protection diodes  transient suppressor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Transient voltage suppressor designed to protect high speed data lines from ESD, EFT, and lightning" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SOT?363*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "NUP4202_0_1"
        (rectangle (start -5.334 3.048) (end 5.08 -3.048)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (circle (center -3.81 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -3.81 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -1.27 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.445 -1.27)
            (xy -3.175 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.445 -1.27)
            (xy -3.175 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.445 1.905)
            (xy -3.175 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 -2.54)
            (xy -3.81 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 -2.54)
            (xy 3.81 -2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 -0.635)
            (xy -3.81 0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 0.635)
            (xy -3.81 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 2.54)
            (xy 3.81 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 2.54)
            (xy 3.81 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 -1.27)
            (xy -0.635 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 -1.27)
            (xy -0.635 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 1.905)
            (xy -0.635 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 -2.54)
            (xy -1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 -2.54)
            (xy -1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 0.635)
            (xy -1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 0.635)
            (xy -1.27 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -0.889)
            (xy 0 -3.81)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -0.889)
            (xy 0 1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 -0.889)
            (xy 0 1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.016)
            (xy 0 3.81)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 -1.27)
            (xy 1.905 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 -1.27)
            (xy 1.905 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 1.905)
            (xy 1.905 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -2.54)
            (xy 1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0.635)
            (xy 1.27 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0.635)
            (xy 1.27 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.175 -1.27)
            (xy 4.445 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.175 -1.27)
            (xy 4.445 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.175 1.905)
            (xy 4.445 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.81 -2.54)
            (xy 3.81 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.81 0.635)
            (xy 3.81 -0.635)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.81 0.635)
            (xy 3.81 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.762 0.254)
            (xy -0.762 0.254)
            (xy -0.762 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.445 -1.905)
            (xy -3.175 -1.905)
            (xy -3.81 -1.27)
            (xy -4.445 -1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -4.445 1.27)
            (xy -3.175 1.27)
            (xy -3.81 1.905)
            (xy -4.445 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.54 -3.81)
            (xy -2.54 -0.762)
            (xy -1.905 0)
            (xy -1.27 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.54 3.81)
            (xy -2.54 0.635)
            (xy -3.048 0)
            (xy -3.937 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 -1.905)
            (xy -0.635 -1.905)
            (xy -1.27 -1.27)
            (xy -1.905 -1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.905 1.27)
            (xy -0.635 1.27)
            (xy -1.27 1.905)
            (xy -1.905 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.508)
            (xy 0.762 -0.508)
            (xy 0 0.254)
            (xy -0.762 -0.508)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.508)
            (xy 0.762 -0.508)
            (xy 0 0.254)
            (xy -0.762 -0.508)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 -1.905)
            (xy 1.905 -1.905)
            (xy 1.27 -1.27)
            (xy 0.635 -1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 1.27)
            (xy 1.905 1.27)
            (xy 1.27 1.905)
            (xy 0.635 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 -3.81)
            (xy 2.54 -0.762)
            (xy 1.905 0)
            (xy 1.143 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 3.81)
            (xy 2.54 0.508)
            (xy 2.921 0)
            (xy 3.81 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.175 -1.905)
            (xy 4.445 -1.905)
            (xy 3.81 -1.27)
            (xy 3.175 -1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 3.175 1.27)
            (xy 4.445 1.27)
            (xy 3.81 1.905)
            (xy 3.175 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 -2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 1.27 2.54) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 3.81 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 3.81 0) (radius 0.127)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "NUP4202_1_1"
        (pin passive line (at -2.54 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -2.54 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Regulator_Linear:AP2112K-3.3" (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "U" (at -5.08 5.715 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "AP2112K-3.3" (at 0 5.715 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5" (at 0 8.255 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "https://www.diodes.com/assets/Datasheets/AP2112.pdf" (at 0 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "linear regulator ldo fixed positive" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "600mA low dropout linear regulator, with enable pin, 3.8V-6V input voltage range, 3.3V fixed positive output, SOT-23-5" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SOT?23?5*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "AP2112K-3.3_0_1"
        (rectangle (start -5.08 4.445) (end 5.08 -5.08)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "AP2112K-3.3_1_1"
        (pin power_in line (at -7.62 2.54 0) (length 2.54)
          (name "VIN" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 -7.62 90) (length 2.54)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 0 0) (length 2.54)
          (name "EN" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin no_connect line (at 5.08 0 180) (length 2.54) hide
          (name "NC" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin power_out line (at 7.62 2.54 180) (length 2.54)
          (name "VOUT" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Switch:SW_DIP_x04" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "SW" (at 0 8.89 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "SW_DIP_x04" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "dip switch" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "4x DIP Switch, Single Pole Single Throw (SPST) switch, small symbol" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SW?DIP?x4*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "SW_DIP_x04_0_0"
        (circle (center -2.032 -2.54) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -2.032 2.54) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center -2.032 5.08) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.524 -2.3876)
            (xy 2.3622 -1.3462)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.524 0.127)
            (xy 2.3622 1.1684)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.524 2.667)
            (xy 2.3622 3.7084)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.524 5.207)
            (xy 2.3622 6.2484)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 -2.54) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 2.54) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 5.08) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "SW_DIP_x04_0_1"
        (rectangle (start -3.81 7.62) (end 3.81 -5.08)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "SW_DIP_x04_1_1"
        (pin passive line (at -7.62 5.08 0) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -7.62 2.54 0) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -7.62 0 0) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -7.62 -2.54 0) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 -2.54 180) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 0 180) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 2.54 180) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 5.08 180) (length 5.08)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Switch:SW_Push" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "SW" (at 1.27 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "SW_Push" (at 0 -1.524 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "switch normally-open pushbutton push-button" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Push button switch, generic, two pins" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "SW_Push_0_1"
        (circle (center -2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 3.048)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 1.27)
            (xy -2.54 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 2.032 0) (radius 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (pin passive line (at -5.08 0 0) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 0 180) (length 2.54)
          (name "2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:+3.3V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "+3.3V" (at 0 3.556 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"+3.3V\"" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "+3.3V_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "+3.3V_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "+3V3" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:+5V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "+5V" (at 0 3.556 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"+5V\"" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "+5V_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "+5V_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "+5V" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 356.87 41.91) (diameter 0) (color 0 0 0 0)
    (uuid 02f60d65-219d-48b1-83a8-a9767f256dc5)
  )
  (junction (at 353.06 85.09) (diameter 0) (color 0 0 0 0)
    (uuid 055bc37e-4f15-403d-b4ca-2c6f97ec9fdf)
  )
  (junction (at 340.36 137.16) (diameter 0) (color 0 0 0 0)
    (uuid 073734cc-78c2-4e10-993a-359bb018c09b)
  )
  (junction (at 355.6 46.99) (diameter 0) (color 0 0 0 0)
    (uuid 0c4d8604-3d65-439f-9c3b-8b086a0bbd07)
  )
  (junction (at 353.06 44.45) (diameter 0) (color 0 0 0 0)
    (uuid 0f84f496-33fa-4840-9aeb-8c20f0ee7049)
  )
  (junction (at 38.1 138.43) (diameter 0) (color 0 0 0 0)
    (uuid 10285b50-b38f-43b7-abb9-49d6821f5408)
  )
  (junction (at 95.25 217.17) (diameter 0) (color 0 0 0 0)
    (uuid 16a3cd98-5845-4d91-bb2b-1d860ba9398b)
  )
  (junction (at 157.48 129.54) (diameter 0) (color 0 0 0 0)
    (uuid 18351b5f-b092-4641-9441-4c6b4c745b7a)
  )
  (junction (at 337.82 50.8) (diameter 0) (color 0 0 0 0)
    (uuid 19564ba9-b2c6-4fdd-917d-16701e4cb0ab)
  )
  (junction (at 358.14 160.02) (diameter 0) (color 0 0 0 0)
    (uuid 1edd7648-0b86-43df-b3af-f161e9373134)
  )
  (junction (at 64.77 46.99) (diameter 0) (color 0 0 0 0)
    (uuid 20b1ad58-d69f-47b2-a5dc-ce90bbb9a735)
  )
  (junction (at 358.14 157.48) (diameter 0) (color 0 0 0 0)
    (uuid 219bfb05-f275-49e1-98d6-c5e1584e2092)
  )
  (junction (at 91.44 29.21) (diameter 0) (color 0 0 0 0)
    (uuid 21cf8a36-4315-4059-8f5e-eee364cffff8)
  )
  (junction (at 356.87 154.94) (diameter 0) (color 0 0 0 0)
    (uuid 222015fd-0422-4a87-acf0-713555c7cc28)
  )
  (junction (at 355.6 82.55) (diameter 0) (color 0 0 0 0)
    (uuid 250794e7-e5e2-4ebf-9762-fea3e5ae45f4)
  )
  (junction (at 379.73 72.39) (diameter 0) (color 0 0 0 0)
    (uuid 2853dbcc-4e58-4f6c-8a4e-d8ab4f6d563e)
  )
  (junction (at 93.98 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 28be84aa-b431-4431-8eeb-d676de337ffa)
  )
  (junction (at 351.79 160.02) (diameter 0) (color 0 0 0 0)
    (uuid 2afcef10-3a2f-44be-bb77-5da296193363)
  )
  (junction (at 96.52 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 2e4e49f8-7bad-4ad6-8ffe-fced3598d641)
  )
  (junction (at 379.73 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 2ffec877-2f9b-47b1-adcf-626fe7eab2d4)
  )
  (junction (at 64.77 124.46) (diameter 0) (color 0 0 0 0)
    (uuid 31cc92da-c160-4594-80c1-3b9e4ec4c2dc)
  )
  (junction (at 353.06 50.8) (diameter 0) (color 0 0 0 0)
    (uuid 31e260d5-5a38-4990-8aec-df39487dcaea)
  )
  (junction (at 355.6 123.19) (diameter 0) (color 0 0 0 0)
    (uuid 338fa06b-3604-4403-b7a6-7e516e09cc55)
  )
  (junction (at 313.69 121.92) (diameter 0) (color 0 0 0 0)
    (uuid 35922a5d-f0f1-4da8-985d-b1e02479239a)
  )
  (junction (at 351.79 127) (diameter 0) (color 0 0 0 0)
    (uuid 3875a126-5964-43dc-a478-0115afdfce3f)
  )
  (junction (at 325.12 92.71) (diameter 0) (color 0 0 0 0)
    (uuid 3ac39fff-776d-466d-b891-a129eb5aa804)
  )
  (junction (at 137.16 119.38) (diameter 0) (color 0 0 0 0)
    (uuid 3e7fcf6f-de09-4fc1-a356-464d2cbdc165)
  )
  (junction (at 106.68 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 4111587c-bb19-4c9e-a36d-88cdc959c3d4)
  )
  (junction (at 379.73 38.1) (diameter 0) (color 0 0 0 0)
    (uuid 423dcba5-c126-424e-86e6-b85e3aff985e)
  )
  (junction (at 351.79 82.55) (diameter 0) (color 0 0 0 0)
    (uuid 4365d726-75c9-42f7-903e-c0e98fb060b5)
  )
  (junction (at 119.38 29.21) (diameter 0) (color 0 0 0 0)
    (uuid 47a78dd5-aa4c-4603-9be9-f286a40d3f2d)
  )
  (junction (at 34.29 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 4cb341a3-e9cf-45fc-a94c-c7fae6c71aea)
  )
  (junction (at 38.1 146.05) (diameter 0) (color 0 0 0 0)
    (uuid 500c42f9-0819-4d0f-8db9-740d9ee8cb19)
  )
  (junction (at 344.17 78.74) (diameter 0) (color 0 0 0 0)
    (uuid 51d54b70-9382-42b6-9457-0d964e6c78df)
  )
  (junction (at 113.03 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 550e974f-5e6d-4d5d-9fc0-da46fda66c34)
  )
  (junction (at 293.37 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 55978dae-a7dc-46ef-9045-81e604202503)
  )
  (junction (at 340.36 152.4) (diameter 0) (color 0 0 0 0)
    (uuid 55f422a0-e870-4f80-82ec-2155c6e123ca)
  )
  (junction (at 113.03 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 56263968-316f-4cfb-a423-8a76a21c8f5f)
  )
  (junction (at 337.82 85.09) (diameter 0) (color 0 0 0 0)
    (uuid 59bc5d09-c7ff-4caf-b71c-7c61ad1191b4)
  )
  (junction (at 63.5 43.18) (diameter 0) (color 0 0 0 0)
    (uuid 5e90cd26-a0cf-4805-b8da-e6e3ea4828fe)
  )
  (junction (at 379.73 148.59) (diameter 0) (color 0 0 0 0)
    (uuid 639cea97-1526-44f5-b31d-1ec11572ffa2)
  )
  (junction (at 93.98 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 651d63c0-993f-419f-858c-4eea1944da5f)
  )
  (junction (at 87.63 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 6669741f-39f7-4eed-b287-2a8d208f506b)
  )
  (junction (at 337.82 76.2) (diameter 0) (color 0 0 0 0)
    (uuid 67a9e19b-5df0-4c1b-b39a-b9c9b7d1a4f3)
  )
  (junction (at 379.73 57.15) (diameter 0) (color 0 0 0 0)
    (uuid 6b23f9c5-5657-4e9a-96cb-5c677db907a6)
  )
  (junction (at 355.6 48.26) (diameter 0) (color 0 0 0 0)
    (uuid 6bc4932c-31dc-4cf5-964f-9c26b6c1a246)
  )
  (junction (at 106.68 40.64) (diameter 0) (color 0 0 0 0)
    (uuid 6d6a39b3-8525-4a9f-a601-67af65e312fa)
  )
  (junction (at 379.73 165.1) (diameter 0) (color 0 0 0 0)
    (uuid 6fc8e37d-a9e3-487c-bfde-25f5b3bdaa0f)
  )
  (junction (at 137.16 124.46) (diameter 0) (color 0 0 0 0)
    (uuid 703fca13-1032-44ae-9c67-49a80ca0ddeb)
  )
  (junction (at 92.71 217.17) (diameter 0) (color 0 0 0 0)
    (uuid 70e53df4-b646-4b2e-a290-a914e00297de)
  )
  (junction (at 48.26 64.77) (diameter 0) (color 0 0 0 0)
    (uuid 733512b8-fb63-47c6-852a-7f631dd2ad51)
  )
  (junction (at 312.42 154.94) (diameter 0) (color 0 0 0 0)
    (uuid 76f0bacd-2a3a-4bf9-9028-ea9309c3e5f9)
  )
  (junction (at 340.36 130.81) (diameter 0) (color 0 0 0 0)
    (uuid 77a1f7ae-90b6-4f6f-a008-4e0321d84a1e)
  )
  (junction (at 81.28 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 7b702a1d-fe94-404f-b362-89f87f8da096)
  )
  (junction (at 311.15 43.18) (diameter 0) (color 0 0 0 0)
    (uuid 7bc39aa7-5548-429d-95c9-c70c465ec6cf)
  )
  (junction (at 97.79 217.17) (diameter 0) (color 0 0 0 0)
    (uuid 7c833044-38ec-40fd-ba42-62fd0cb5a56e)
  )
  (junction (at 359.41 152.4) (diameter 0) (color 0 0 0 0)
    (uuid 7f211d63-e86d-4037-9ef5-f799531f6aa9)
  )
  (junction (at 24.13 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 7f2d4a1b-60c4-4035-9c9e-2b125d24ce84)
  )
  (junction (at 58.42 132.08) (diameter 0) (color 0 0 0 0)
    (uuid 81a98693-39b2-47a6-81da-2ed912032ac2)
  )
  (junction (at 142.24 132.08) (diameter 0) (color 0 0 0 0)
    (uuid 85ef419b-2359-4da3-87dd-44ab32ef2726)
  )
  (junction (at 379.73 87.63) (diameter 0) (color 0 0 0 0)
    (uuid 874def14-a130-4579-9fb7-b2e76ea0cfe2)
  )
  (junction (at 379.73 168.91) (diameter 0) (color 0 0 0 0)
    (uuid 87a5b2ab-a88c-48b4-a706-1ae0967ca0ee)
  )
  (junction (at 99.06 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 88758e6b-27c2-40f8-b90c-c09276d03e49)
  )
  (junction (at 316.23 60.96) (diameter 0) (color 0 0 0 0)
    (uuid 8c7c4d62-8b1b-4960-8205-7d70e369c35c)
  )
  (junction (at 350.52 48.26) (diameter 0) (color 0 0 0 0)
    (uuid 8e1a33ab-9eba-4a1d-8570-f9505e989c53)
  )
  (junction (at 91.44 109.22) (diameter 0) (color 0 0 0 0)
    (uuid 8f1128f2-aed3-4b9a-be1d-97e3c9e3e1e4)
  )
  (junction (at 81.28 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 8ffe8b22-aa91-4b60-868d-ad9c1274ba05)
  )
  (junction (at 293.37 54.61) (diameter 0) (color 0 0 0 0)
    (uuid 931adf6a-d339-4f27-a465-4fbf5634641e)
  )
  (junction (at 312.42 77.47) (diameter 0) (color 0 0 0 0)
    (uuid 9ebb1d55-c4a8-4cf1-84b5-09044b82422c)
  )
  (junction (at 91.44 40.64) (diameter 0) (color 0 0 0 0)
    (uuid 9eea8331-9c75-4147-900e-277643181cc1)
  )
  (junction (at 379.73 116.84) (diameter 0) (color 0 0 0 0)
    (uuid 9f509fd5-6801-44ac-8c44-1c6ee7fcd7a9)
  )
  (junction (at 54.61 146.05) (diameter 0) (color 0 0 0 0)
    (uuid a25bc5a7-ef9b-4b64-928c-93a7db388e55)
  )
  (junction (at 356.87 76.2) (diameter 0) (color 0 0 0 0)
    (uuid a2d2a4d9-bfdd-444c-8ba2-9dc393004076)
  )
  (junction (at 66.04 26.67) (diameter 0) (color 0 0 0 0)
    (uuid a9e77f45-f3a5-480e-a1a1-c2557a806e6e)
  )
  (junction (at 356.87 163.83) (diameter 0) (color 0 0 0 0)
    (uuid ab617312-2763-4a51-bab7-04b2ea54561b)
  )
  (junction (at 327.66 170.18) (diameter 0) (color 0 0 0 0)
    (uuid ad4f4d93-abb1-43e2-9b1f-ae19dbc9e9ad)
  )
  (junction (at 100.33 109.22) (diameter 0) (color 0 0 0 0)
    (uuid b22d8172-7a1f-465a-8bb8-fc9bb67d4016)
  )
  (junction (at 45.72 39.37) (diameter 0) (color 0 0 0 0)
    (uuid b3fe6ac2-d9c8-4abf-9244-1f803254fec6)
  )
  (junction (at 88.9 109.22) (diameter 0) (color 0 0 0 0)
    (uuid b9fe35a3-0c1d-4bea-a401-b41c84eb7cbf)
  )
  (junction (at 340.36 163.83) (diameter 0) (color 0 0 0 0)
    (uuid bc2dcb9b-a853-4044-a380-146d790d1faa)
  )
  (junction (at 344.17 154.94) (diameter 0) (color 0 0 0 0)
    (uuid bcf335e1-88af-480d-8459-7a7ca16a8ded)
  )
  (junction (at 54.61 138.43) (diameter 0) (color 0 0 0 0)
    (uuid be087a6e-a90c-4a56-b819-25571d1d36dd)
  )
  (junction (at 344.17 123.19) (diameter 0) (color 0 0 0 0)
    (uuid bfa2cc80-73dc-40d2-a68f-660ae71fe788)
  )
  (junction (at 337.82 58.42) (diameter 0) (color 0 0 0 0)
    (uuid c09ced42-419e-416c-812b-e9fbf43b3abf)
  )
  (junction (at 342.9 44.45) (diameter 0) (color 0 0 0 0)
    (uuid c9215926-8bd9-4801-a79b-1ab54bf08662)
  )
  (junction (at 318.77 138.43) (diameter 0) (color 0 0 0 0)
    (uuid ccae5e61-1d59-4352-8b68-626fd934a750)
  )
  (junction (at 90.17 217.17) (diameter 0) (color 0 0 0 0)
    (uuid cf4a1b4d-4895-4c96-86ee-c05c6ee9f3da)
  )
  (junction (at 327.66 109.22) (diameter 0) (color 0 0 0 0)
    (uuid d32a5342-dbc3-4d58-b708-283f35389d88)
  )
  (junction (at 100.33 91.44) (diameter 0) (color 0 0 0 0)
    (uuid d5651b11-b1b9-4107-af71-9babf712cb78)
  )
  (junction (at 40.64 109.22) (diameter 0) (color 0 0 0 0)
    (uuid d8effe93-ec06-4bbe-849e-c8c6ad02e57b)
  )
  (junction (at 356.87 127) (diameter 0) (color 0 0 0 0)
    (uuid d97b646c-dbba-4a90-a1b2-b20ea5665991)
  )
  (junction (at 58.42 24.13) (diameter 0) (color 0 0 0 0)
    (uuid da2cca28-4347-433a-9dfb-a71f7711150d)
  )
  (junction (at 355.6 81.28) (diameter 0) (color 0 0 0 0)
    (uuid dd42c3c7-22bd-46d2-aae9-292e6c291452)
  )
  (junction (at 137.16 121.92) (diameter 0) (color 0 0 0 0)
    (uuid dd642844-011b-4d33-9fb3-d354cd1191a0)
  )
  (junction (at 355.6 130.81) (diameter 0) (color 0 0 0 0)
    (uuid de00bb12-112c-4a26-a498-cddc1cbf68f3)
  )
  (junction (at 379.73 132.08) (diameter 0) (color 0 0 0 0)
    (uuid de259636-e31e-4422-919a-5fc2471c2d0a)
  )
  (junction (at 106.68 91.44) (diameter 0) (color 0 0 0 0)
    (uuid df75701d-e00c-4179-a1f9-290c7787e6c1)
  )
  (junction (at 58.42 124.46) (diameter 0) (color 0 0 0 0)
    (uuid e53a0b6b-21c4-4f9a-92a1-eb0796714f18)
  )
  (junction (at 27.94 64.77) (diameter 0) (color 0 0 0 0)
    (uuid edf2573a-b872-4668-ac82-84677f63e0d1)
  )
  (junction (at 358.14 120.65) (diameter 0) (color 0 0 0 0)
    (uuid eea860aa-0b36-40e1-962e-547832289cd6)
  )
  (junction (at 379.73 53.34) (diameter 0) (color 0 0 0 0)
    (uuid eff4c93d-0322-4ec9-9b50-700dddb25c1a)
  )
  (junction (at 325.12 30.48) (diameter 0) (color 0 0 0 0)
    (uuid eff8dbf5-3412-46d8-bb07-201b208ad908)
  )
  (junction (at 45.72 41.91) (diameter 0) (color 0 0 0 0)
    (uuid f33f2bd7-c776-4214-b779-bd28f068eab4)
  )
  (junction (at 87.63 109.22) (diameter 0) (color 0 0 0 0)
    (uuid fa3db1bc-3f10-4d21-80df-579f99e63400)
  )
  (junction (at 379.73 135.89) (diameter 0) (color 0 0 0 0)
    (uuid facce0f5-db23-4345-be53-c63ed7fad818)
  )
  (junction (at 353.06 78.74) (diameter 0) (color 0 0 0 0)
    (uuid fba0bcb0-72a5-42b9-af47-88a4f726571f)
  )
  (junction (at 96.52 29.21) (diameter 0) (color 0 0 0 0)
    (uuid fe52537f-eafc-4332-beac-41b3fff91003)
  )
  (junction (at 356.87 125.73) (diameter 0) (color 0 0 0 0)
    (uuid fe9ccbbc-a9cf-4886-a19b-9a3d08cdb206)
  )

  (no_connect (at 43.18 54.61) (uuid 256ea227-593f-43c9-89f1-da07b752ddc4))
  (no_connect (at 43.18 52.07) (uuid 5f8c7eb9-7012-4ef5-be9c-0077d9d1e43a))

  (wire (pts (xy 307.34 74.93) (xy 314.96 74.93))
    (stroke (width 0) (type default))
    (uuid 00f78a52-1e90-4655-84af-a5f3ff1e8f3c)
  )
  (wire (pts (xy 149.86 189.23) (xy 149.86 191.77))
    (stroke (width 0) (type default))
    (uuid 01867644-a044-4449-bf89-c71a9cafeab7)
  )
  (wire (pts (xy 153.67 138.43) (xy 157.48 138.43))
    (stroke (width 0) (type default))
    (uuid 021ea5d1-5dfd-44c0-b36d-7bb7f499f89a)
  )
  (wire (pts (xy 115.57 127) (xy 120.65 127))
    (stroke (width 0) (type default))
    (uuid 03bb1870-12c9-4d6c-b0ee-bc5b7e79a42a)
  )
  (wire (pts (xy 355.6 87.63) (xy 355.6 82.55))
    (stroke (width 0) (type default))
    (uuid 03d7d1db-b453-49c9-be99-8d86ac86b9ef)
  )
  (wire (pts (xy 63.5 43.18) (xy 63.5 52.07))
    (stroke (width 0) (type default))
    (uuid 043e5922-ff5e-4e7b-bf81-570561c9376e)
  )
  (wire (pts (xy 358.14 120.65) (xy 358.14 116.84))
    (stroke (width 0) (type default))
    (uuid 05050c25-2f3b-4cbe-88d1-be281f972ec7)
  )
  (wire (pts (xy 45.72 41.91) (xy 63.5 41.91))
    (stroke (width 0) (type default))
    (uuid 053d36e9-89a3-4849-850a-2eba2048c1fb)
  )
  (wire (pts (xy 115.57 200.66) (xy 138.43 200.66))
    (stroke (width 0) (type default))
    (uuid 05f8ff15-a910-4952-bb85-b6137f33f108)
  )
  (wire (pts (xy 372.11 168.91) (xy 379.73 168.91))
    (stroke (width 0) (type default))
    (uuid 0613f409-5d1f-4f4e-a440-2f5f6dac6c88)
  )
  (wire (pts (xy 356.87 44.45) (xy 353.06 44.45))
    (stroke (width 0) (type default))
    (uuid 06a7d3df-1f9f-4898-bd63-6d5d0d2d0cd6)
  )
  (wire (pts (xy 307.34 43.18) (xy 311.15 43.18))
    (stroke (width 0) (type default))
    (uuid 06f60fa4-b574-4a87-b92f-9b5533cf6cb4)
  )
  (wire (pts (xy 355.6 48.26) (xy 355.6 46.99))
    (stroke (width 0) (type default))
    (uuid 09d11799-becf-4241-8695-e000a67625fe)
  )
  (wire (pts (xy 340.36 130.81) (xy 340.36 137.16))
    (stroke (width 0) (type default))
    (uuid 0b4974c8-7d0f-4ef3-b43e-e3d5df88f737)
  )
  (wire (pts (xy 43.18 138.43) (xy 38.1 138.43))
    (stroke (width 0) (type default))
    (uuid 0bc1765e-e5fd-4bc6-8604-1dd247217c87)
  )
  (wire (pts (xy 106.68 109.22) (xy 100.33 109.22))
    (stroke (width 0) (type default))
    (uuid 0c05f4dd-eac5-4156-b65b-21f8b1f8b902)
  )
  (wire (pts (xy 93.98 109.22) (xy 96.52 109.22))
    (stroke (width 0) (type default))
    (uuid 0c0b06b7-96d7-474b-9609-4c48aee31e88)
  )
  (wire (pts (xy 344.17 73.66) (xy 344.17 78.74))
    (stroke (width 0) (type default))
    (uuid 0c338ea3-b07d-4404-9ec8-feface7d9dbf)
  )
  (wire (pts (xy 96.52 109.22) (xy 96.52 111.76))
    (stroke (width 0) (type default))
    (uuid 0cac3e54-b6b2-4454-bf60-5914704273b6)
  )
  (wire (pts (xy 340.36 152.4) (xy 359.41 152.4))
    (stroke (width 0) (type default))
    (uuid 0cc98bf3-5067-49db-a06e-f2531c95dde6)
  )
  (wire (pts (xy 370.84 53.34) (xy 379.73 53.34))
    (stroke (width 0) (type default))
    (uuid 0d1b7956-dca1-4121-ad70-ca2b682b8ed0)
  )
  (wire (pts (xy 45.72 39.37) (xy 64.77 39.37))
    (stroke (width 0) (type default))
    (uuid 0d7b2cb8-0dce-4f50-ac66-20228471eb5f)
  )
  (wire (pts (xy 337.82 76.2) (xy 337.82 58.42))
    (stroke (width 0) (type default))
    (uuid 0f04b4e2-6171-41a8-b1b1-12ca51738107)
  )
  (wire (pts (xy 146.05 158.75) (xy 135.89 158.75))
    (stroke (width 0) (type default))
    (uuid 0f0f4045-c3eb-41b1-92fa-82dd3832890f)
  )
  (wire (pts (xy 38.1 138.43) (xy 38.1 146.05))
    (stroke (width 0) (type default))
    (uuid 0f244b7d-f5bc-473a-a3ea-d6da693c1778)
  )
  (wire (pts (xy 316.23 60.96) (xy 355.6 60.96))
    (stroke (width 0) (type default))
    (uuid 0fcb0796-f0be-4e0f-a134-bf96bf94f637)
  )
  (wire (pts (xy 119.38 91.44) (xy 119.38 93.98))
    (stroke (width 0) (type default))
    (uuid 0fd7af86-9f42-47af-aa20-f63957442025)
  )
  (wire (pts (xy 146.05 148.59) (xy 140.97 148.59))
    (stroke (width 0) (type default))
    (uuid 107fb0f0-8385-455a-b094-90c3d49d1d60)
  )
  (wire (pts (xy 54.61 146.05) (xy 50.8 146.05))
    (stroke (width 0) (type default))
    (uuid 10bdf757-ce01-48c2-8c3f-b1102cf9a41c)
  )
  (wire (pts (xy 344.17 118.11) (xy 344.17 123.19))
    (stroke (width 0) (type default))
    (uuid 11088aa3-579f-4320-a772-b51f9dd86b1e)
  )
  (wire (pts (xy 353.06 50.8) (xy 353.06 44.45))
    (stroke (width 0) (type default))
    (uuid 11224bbb-354d-4e52-8ffe-8e5aeddafb43)
  )
  (wire (pts (xy 337.82 123.19) (xy 337.82 119.38))
    (stroke (width 0) (type default))
    (uuid 11a1b388-f0b2-4f1d-81f6-0ba89c008a29)
  )
  (wire (pts (xy 355.6 38.1) (xy 355.6 46.99))
    (stroke (width 0) (type default))
    (uuid 11da5f2b-89b9-40d9-9d90-196f82fc02f7)
  )
  (wire (pts (xy 115.57 195.58) (xy 140.97 195.58))
    (stroke (width 0) (type default))
    (uuid 122847d3-1c19-4398-bf16-419e8fc74408)
  )
  (wire (pts (xy 144.78 205.74) (xy 167.64 205.74))
    (stroke (width 0) (type default))
    (uuid 13244e83-71d7-4371-baf3-b2322d189aab)
  )
  (wire (pts (xy 100.33 109.22) (xy 99.06 109.22))
    (stroke (width 0) (type default))
    (uuid 136882f9-ca89-4d4c-9e33-aa10c349a5f0)
  )
  (wire (pts (xy 81.28 101.6) (xy 81.28 109.22))
    (stroke (width 0) (type default))
    (uuid 13df4b1f-7c52-4a29-8f82-d692d1591398)
  )
  (wire (pts (xy 137.16 124.46) (xy 137.16 127))
    (stroke (width 0) (type default))
    (uuid 140a6b44-86e4-473d-9864-27eacf319a6f)
  )
  (wire (pts (xy 372.11 87.63) (xy 379.73 87.63))
    (stroke (width 0) (type default))
    (uuid 1c07355d-4dee-4f1a-b987-bb1cbb3ff594)
  )
  (wire (pts (xy 379.73 87.63) (xy 379.73 91.44))
    (stroke (width 0) (type default))
    (uuid 1f591611-2ebe-4753-bd3e-8a51e018d086)
  )
  (wire (pts (xy 83.82 40.64) (xy 91.44 40.64))
    (stroke (width 0) (type default))
    (uuid 20897d5d-cbee-405f-9a48-ddc4589aeafc)
  )
  (wire (pts (xy 138.43 213.36) (xy 167.64 213.36))
    (stroke (width 0) (type default))
    (uuid 2310e473-a353-47ff-9397-c69a101d8b73)
  )
  (wire (pts (xy 100.33 101.6) (xy 100.33 109.22))
    (stroke (width 0) (type default))
    (uuid 232608f8-912a-4166-a0de-39b1cb11b107)
  )
  (wire (pts (xy 114.3 29.21) (xy 119.38 29.21))
    (stroke (width 0) (type default))
    (uuid 23b35d6a-60c7-480e-a760-1424be137509)
  )
  (wire (pts (xy 356.87 72.39) (xy 356.87 76.2))
    (stroke (width 0) (type default))
    (uuid 24b3bc61-d595-4851-a8f5-193039965902)
  )
  (wire (pts (xy 306.07 160.02) (xy 317.5 160.02))
    (stroke (width 0) (type default))
    (uuid 24f0e309-abe3-41d5-b331-76c4baf70197)
  )
  (wire (pts (xy 353.06 78.74) (xy 344.17 78.74))
    (stroke (width 0) (type default))
    (uuid 25d5f47a-3ff7-4d4f-aa32-7be1ea89f585)
  )
  (wire (pts (xy 358.14 165.1) (xy 358.14 160.02))
    (stroke (width 0) (type default))
    (uuid 26096af7-5ea1-4afa-bbc1-cd20c185469b)
  )
  (wire (pts (xy 311.15 45.72) (xy 311.15 43.18))
    (stroke (width 0) (type default))
    (uuid 26909415-bd27-4d21-9d68-9bf0b7ca389c)
  )
  (wire (pts (xy 115.57 124.46) (xy 120.65 124.46))
    (stroke (width 0) (type default))
    (uuid 273429d9-ba8c-49f6-bb1d-49e82d6763fb)
  )
  (wire (pts (xy 137.16 116.84) (xy 137.16 119.38))
    (stroke (width 0) (type default))
    (uuid 2817d034-ad0d-4096-8245-d6b0e346532d)
  )
  (wire (pts (xy 140.97 195.58) (xy 140.97 208.28))
    (stroke (width 0) (type default))
    (uuid 284e7c66-270c-4544-bbfa-81414ca585fe)
  )
  (wire (pts (xy 138.43 167.64) (xy 138.43 193.04))
    (stroke (width 0) (type default))
    (uuid 28704679-79d8-49d7-ab6a-533eda9c8028)
  )
  (wire (pts (xy 355.6 135.89) (xy 355.6 130.81))
    (stroke (width 0) (type default))
    (uuid 28c153b0-f225-4a76-9ccf-0b1f7368c835)
  )
  (wire (pts (xy 355.6 60.96) (xy 355.6 64.77))
    (stroke (width 0) (type default))
    (uuid 296909a0-7cfa-42e1-bd96-205099ae4073)
  )
  (wire (pts (xy 306.07 152.4) (xy 317.5 152.4))
    (stroke (width 0) (type default))
    (uuid 297ae494-3f7f-4499-a38c-9df3b8acbc5d)
  )
  (wire (pts (xy 313.69 124.46) (xy 313.69 121.92))
    (stroke (width 0) (type default))
    (uuid 2d267e3f-3887-44c1-9351-4be52e7bda5f)
  )
  (wire (pts (xy 104.14 217.17) (xy 97.79 217.17))
    (stroke (width 0) (type default))
    (uuid 2eb0239f-e9dd-4763-9b41-d0fe8fcd2689)
  )
  (wire (pts (xy 69.85 143.51) (xy 69.85 146.05))
    (stroke (width 0) (type default))
    (uuid 2ed29f1f-9177-44ac-aa2d-c7f04f6f7bbb)
  )
  (wire (pts (xy 379.73 53.34) (xy 379.73 57.15))
    (stroke (width 0) (type default))
    (uuid 30035261-36c0-40a1-95ab-adc09e83bdbd)
  )
  (wire (pts (xy 64.77 46.99) (xy 64.77 49.53))
    (stroke (width 0) (type default))
    (uuid 30286af3-3c27-467d-95d4-5fe3fa9adfb4)
  )
  (wire (pts (xy 81.28 91.44) (xy 87.63 91.44))
    (stroke (width 0) (type default))
    (uuid 31d1aea0-d65d-4fa0-b738-d30ae1d6f2da)
  )
  (wire (pts (xy 355.6 123.19) (xy 344.17 123.19))
    (stroke (width 0) (type default))
    (uuid 32944643-17b5-4170-a43f-8b5cbba31924)
  )
  (wire (pts (xy 119.38 39.37) (xy 119.38 40.64))
    (stroke (width 0) (type default))
    (uuid 32a1a02f-042d-49c0-928a-271793f48adc)
  )
  (wire (pts (xy 81.28 91.44) (xy 81.28 93.98))
    (stroke (width 0) (type default))
    (uuid 333a67df-0a12-46b3-9dc1-d29c90ee56d2)
  )
  (wire (pts (xy 337.82 76.2) (xy 356.87 76.2))
    (stroke (width 0) (type default))
    (uuid 3357dee0-8492-447e-ba8e-fffa0ee68aee)
  )
  (wire (pts (xy 318.77 138.43) (xy 318.77 134.62))
    (stroke (width 0) (type default))
    (uuid 3503a189-4212-493e-b5b1-5281cbbf42a3)
  )
  (wire (pts (xy 58.42 21.59) (xy 58.42 24.13))
    (stroke (width 0) (type default))
    (uuid 35e2b280-14b6-4085-83a9-e98bbc39b588)
  )
  (wire (pts (xy 27.94 62.23) (xy 27.94 64.77))
    (stroke (width 0) (type default))
    (uuid 36137f4e-b25a-43d9-8340-bff323d3912e)
  )
  (wire (pts (xy 67.31 135.89) (xy 69.85 135.89))
    (stroke (width 0) (type default))
    (uuid 361d88e7-1ae5-4b34-a900-5368f51c9982)
  )
  (wire (pts (xy 355.6 53.34) (xy 355.6 48.26))
    (stroke (width 0) (type default))
    (uuid 36e3a3ff-9199-4cfa-8744-27c0aaf65564)
  )
  (wire (pts (xy 64.77 124.46) (xy 69.85 124.46))
    (stroke (width 0) (type default))
    (uuid 3911e081-11fd-4dd9-8808-e56f218002e9)
  )
  (wire (pts (xy 146.05 161.29) (xy 133.35 161.29))
    (stroke (width 0) (type default))
    (uuid 3a7af415-cb5b-44d4-816f-2640f3559480)
  )
  (wire (pts (xy 113.03 109.22) (xy 106.68 109.22))
    (stroke (width 0) (type default))
    (uuid 3a7d5adc-6d5b-48b5-a4af-b33aa748c92f)
  )
  (wire (pts (xy 140.97 208.28) (xy 167.64 208.28))
    (stroke (width 0) (type default))
    (uuid 3e7acdff-bb4b-4b5e-a669-4deded490290)
  )
  (wire (pts (xy 115.57 154.94) (xy 130.81 154.94))
    (stroke (width 0) (type default))
    (uuid 3f2ec03c-c1e5-4b49-b68a-8e23ebf104d5)
  )
  (wire (pts (xy 356.87 38.1) (xy 379.73 38.1))
    (stroke (width 0) (type default))
    (uuid 3f5ec47d-65bc-4f3f-8c58-8b06f28e0eeb)
  )
  (wire (pts (xy 340.36 152.4) (xy 340.36 137.16))
    (stroke (width 0) (type default))
    (uuid 3f86741c-9662-4e19-a552-afdd224b2331)
  )
  (wire (pts (xy 130.81 154.94) (xy 130.81 151.13))
    (stroke (width 0) (type default))
    (uuid 400f9936-61c2-4bff-81dc-7164bde164af)
  )
  (wire (pts (xy 93.98 91.44) (xy 93.98 93.98))
    (stroke (width 0) (type default))
    (uuid 4174bc47-1b20-4b6a-8fcb-82c579613766)
  )
  (wire (pts (xy 27.94 64.77) (xy 20.32 64.77))
    (stroke (width 0) (type default))
    (uuid 4191401d-93aa-4f06-81cc-69019caec0bc)
  )
  (wire (pts (xy 364.49 135.89) (xy 355.6 135.89))
    (stroke (width 0) (type default))
    (uuid 41d8e1e5-c70d-40f4-99a7-220fc1183187)
  )
  (wire (pts (xy 142.24 132.08) (xy 142.24 129.54))
    (stroke (width 0) (type default))
    (uuid 44f72e01-7c8d-4f6a-a4b1-136a1ec75e97)
  )
  (wire (pts (xy 43.18 44.45) (xy 45.72 44.45))
    (stroke (width 0) (type default))
    (uuid 45091cd5-85a4-4516-bc7a-639b95c7dd8c)
  )
  (wire (pts (xy 91.44 29.21) (xy 96.52 29.21))
    (stroke (width 0) (type default))
    (uuid 468cffcd-cdd1-48f8-9080-2cf50910b7ae)
  )
  (wire (pts (xy 64.77 132.08) (xy 58.42 132.08))
    (stroke (width 0) (type default))
    (uuid 47a5cb71-593e-41ad-a1b2-d3f9ca32879c)
  )
  (wire (pts (xy 307.34 48.26) (xy 314.96 48.26))
    (stroke (width 0) (type default))
    (uuid 48ae098b-b04a-40d1-9f11-e40b032e9ea7)
  )
  (wire (pts (xy 379.73 135.89) (xy 379.73 148.59))
    (stroke (width 0) (type default))
    (uuid 4a2771b7-0eca-4a36-8903-12facbb42986)
  )
  (wire (pts (xy 351.79 160.02) (xy 358.14 160.02))
    (stroke (width 0) (type default))
    (uuid 4a4e1362-4289-43c3-b5d6-fe89d36fa317)
  )
  (wire (pts (xy 337.82 50.8) (xy 337.82 58.42))
    (stroke (width 0) (type default))
    (uuid 4b3bfcf3-8e74-440c-8fa4-e77fc1463bce)
  )
  (wire (pts (xy 99.06 109.22) (xy 99.06 111.76))
    (stroke (width 0) (type default))
    (uuid 4b67d184-64b1-470a-b4fa-a2361be39a23)
  )
  (wire (pts (xy 87.63 109.22) (xy 88.9 109.22))
    (stroke (width 0) (type default))
    (uuid 4cc7199a-dc88-4a7b-ac61-9d4444eacbf2)
  )
  (wire (pts (xy 115.57 165.1) (xy 139.7 165.1))
    (stroke (width 0) (type default))
    (uuid 4e01a3cb-738c-4a75-81d9-7edac0a9598e)
  )
  (wire (pts (xy 119.38 109.22) (xy 113.03 109.22))
    (stroke (width 0) (type default))
    (uuid 4e9eb994-4d29-41db-aff4-82b261fe136e)
  )
  (wire (pts (xy 379.73 57.15) (xy 379.73 72.39))
    (stroke (width 0) (type default))
    (uuid 4f2c98cc-3b5f-4881-a192-bdd870cdd654)
  )
  (wire (pts (xy 87.63 91.44) (xy 87.63 93.98))
    (stroke (width 0) (type default))
    (uuid 4faa6e6d-f904-43ed-af29-232354c95f73)
  )
  (wire (pts (xy 363.22 53.34) (xy 355.6 53.34))
    (stroke (width 0) (type default))
    (uuid 4ff47824-4de3-4d3f-ae88-7326df0bcb94)
  )
  (wire (pts (xy 115.57 119.38) (xy 120.65 119.38))
    (stroke (width 0) (type default))
    (uuid 512a146b-6bf9-4b16-9f86-ec816c9c2124)
  )
  (wire (pts (xy 314.96 80.01) (xy 312.42 80.01))
    (stroke (width 0) (type default))
    (uuid 51364694-b6f8-405b-81d0-9a52a66c12d1)
  )
  (wire (pts (xy 113.03 91.44) (xy 119.38 91.44))
    (stroke (width 0) (type default))
    (uuid *************-46e0-99c6-cb14e503f7eb)
  )
  (wire (pts (xy 337.82 41.91) (xy 337.82 50.8))
    (stroke (width 0) (type default))
    (uuid 52b91631-ce63-4c32-b435-27d83cdcae30)
  )
  (wire (pts (xy 364.49 87.63) (xy 355.6 87.63))
    (stroke (width 0) (type default))
    (uuid 535d4946-e15f-4fd2-a39c-a9f0bac60237)
  )
  (wire (pts (xy 344.17 123.19) (xy 337.82 123.19))
    (stroke (width 0) (type default))
    (uuid 535f5139-fa28-4c74-8d77-de460853126a)
  )
  (wire (pts (xy 293.37 30.48) (xy 293.37 54.61))
    (stroke (width 0) (type default))
    (uuid 539e83da-f24b-4b46-b88f-c312c1f84af0)
  )
  (wire (pts (xy 337.82 160.02) (xy 351.79 160.02))
    (stroke (width 0) (type default))
    (uuid 560c67fc-804e-42bb-860d-e5b13d72fa30)
  )
  (wire (pts (xy 312.42 80.01) (xy 312.42 77.47))
    (stroke (width 0) (type default))
    (uuid 56169825-669c-453f-9da9-4293e83ce584)
  )
  (wire (pts (xy 34.29 109.22) (xy 33.02 109.22))
    (stroke (width 0) (type default))
    (uuid 56fd1323-b3d4-40fb-a5a3-8c320a767d35)
  )
  (wire (pts (xy 327.66 170.18) (xy 340.36 170.18))
    (stroke (width 0) (type default))
    (uuid 58b4a7a7-88d3-4e82-8f99-6c9872238dae)
  )
  (wire (pts (xy 318.77 134.62) (xy 293.37 134.62))
    (stroke (width 0) (type default))
    (uuid 59cdd01e-b0de-465e-9e78-5ec30fa9826b)
  )
  (wire (pts (xy 356.87 72.39) (xy 379.73 72.39))
    (stroke (width 0) (type default))
    (uuid 5ac61eba-1637-4dc4-945e-d54b727ba4e2)
  )
  (wire (pts (xy 344.17 78.74) (xy 336.55 78.74))
    (stroke (width 0) (type default))
    (uuid 5ac6563a-f360-40f6-825c-4886b2b02935)
  )
  (wire (pts (xy 146.05 203.2) (xy 167.64 203.2))
    (stroke (width 0) (type default))
    (uuid 5afba641-3d53-43b6-b57f-0c04b87be17f)
  )
  (wire (pts (xy 115.57 198.12) (xy 139.7 198.12))
    (stroke (width 0) (type default))
    (uuid 5b9e0ab3-c613-4530-be74-f8ab4dbd5fa8)
  )
  (wire (pts (xy 146.05 138.43) (xy 142.24 138.43))
    (stroke (width 0) (type default))
    (uuid 5c808344-8648-4559-b6bf-0f83338b6bec)
  )
  (wire (pts (xy 91.44 26.67) (xy 91.44 29.21))
    (stroke (width 0) (type default))
    (uuid 5d744e15-2142-4585-a555-bca8d5677998)
  )
  (wire (pts (xy 359.41 154.94) (xy 356.87 154.94))
    (stroke (width 0) (type default))
    (uuid 5e6bddfd-15c5-4a56-a90f-c6ccfc1da47a)
  )
  (wire (pts (xy 307.34 40.64) (xy 314.96 40.64))
    (stroke (width 0) (type default))
    (uuid 5e6e054c-d208-4616-85bd-4b25e6c22570)
  )
  (polyline (pts (xy 289.56 24.13) (xy 373.38 24.13))
    (stroke (width 0) (type default))
    (uuid 5f931f55-6d1d-44e4-8f79-ebcf8d8ba3c6)
  )

  (wire (pts (xy 100.33 91.44) (xy 100.33 93.98))
    (stroke (width 0) (type default))
    (uuid 5fbb1897-af29-4fab-8839-c374a0938701)
  )
  (wire (pts (xy 325.12 64.77) (xy 316.23 64.77))
    (stroke (width 0) (type default))
    (uuid 6078ffc9-bf6a-47c9-8c63-9023ecf523f5)
  )
  (wire (pts (xy 27.94 64.77) (xy 27.94 66.04))
    (stroke (width 0) (type default))
    (uuid 6091a784-9c5c-4db1-b20a-0a198ac069e7)
  )
  (wire (pts (xy 38.1 132.08) (xy 38.1 138.43))
    (stroke (width 0) (type default))
    (uuid 60b04333-43b7-4918-a7ef-77ab3e3ee4e5)
  )
  (wire (pts (xy 356.87 116.84) (xy 356.87 125.73))
    (stroke (width 0) (type default))
    (uuid 638550ba-f2dc-4dc0-a1ec-3e9a7d155a54)
  )
  (wire (pts (xy 293.37 54.61) (xy 293.37 109.22))
    (stroke (width 0) (type default))
    (uuid 639a5c61-414e-40df-b7a2-2b2649c89187)
  )
  (wire (pts (xy 48.26 46.99) (xy 48.26 31.75))
    (stroke (width 0) (type default))
    (uuid 64abed7c-abbf-46e6-8d0d-14680e95597c)
  )
  (wire (pts (xy 146.05 191.77) (xy 146.05 203.2))
    (stroke (width 0) (type default))
    (uuid 652ad7bb-467b-437c-a01f-e9b240d3db2c)
  )
  (wire (pts (xy 359.41 148.59) (xy 359.41 152.4))
    (stroke (width 0) (type default))
    (uuid 6654eda8-5d28-4602-a50a-a131bb8f308b)
  )
  (wire (pts (xy 293.37 109.22) (xy 293.37 134.62))
    (stroke (width 0) (type default))
    (uuid 66615bce-6243-4dd8-ab1a-d8f2796a3014)
  )
  (wire (pts (xy 353.06 91.44) (xy 353.06 85.09))
    (stroke (width 0) (type default))
    (uuid 66d6e56b-2fcb-4b09-8595-24880115e4c5)
  )
  (wire (pts (xy 335.28 44.45) (xy 335.28 40.64))
    (stroke (width 0) (type default))
    (uuid 673233c1-0cb8-4c9d-9837-2a59109f1fd6)
  )
  (wire (pts (xy 96.52 29.21) (xy 99.06 29.21))
    (stroke (width 0) (type default))
    (uuid 679f4586-97d9-4ec6-b0c7-273517b760da)
  )
  (wire (pts (xy 316.23 64.77) (xy 316.23 60.96))
    (stroke (width 0) (type default))
    (uuid 67f20cb7-8d9b-491a-a2f1-f688a6366ed2)
  )
  (wire (pts (xy 379.73 72.39) (xy 379.73 87.63))
    (stroke (width 0) (type default))
    (uuid 6870aef2-0a44-42b6-8d36-22a5a638be7d)
  )
  (wire (pts (xy 87.63 91.44) (xy 93.98 91.44))
    (stroke (width 0) (type default))
    (uuid 68cdab2f-9023-4157-add3-c88a06701b21)
  )
  (wire (pts (xy 363.22 57.15) (xy 353.06 57.15))
    (stroke (width 0) (type default))
    (uuid 6aef6b5d-6ba9-4c41-b83d-e86f83191ce1)
  )
  (wire (pts (xy 91.44 43.18) (xy 91.44 40.64))
    (stroke (width 0) (type default))
    (uuid 6b5d6085-a914-4298-81d3-9578a432c98c)
  )
  (wire (pts (xy 351.79 82.55) (xy 355.6 82.55))
    (stroke (width 0) (type default))
    (uuid 6bd3eb3d-b63c-44b2-a8d9-7f5fdca6fe61)
  )
  (wire (pts (xy 379.73 148.59) (xy 379.73 165.1))
    (stroke (width 0) (type default))
    (uuid 6c209572-bc8e-481c-acfb-2450271005a9)
  )
  (wire (pts (xy 34.29 109.22) (xy 40.64 109.22))
    (stroke (width 0) (type default))
    (uuid 6cc3b9cd-599d-4335-a54f-e9659eae73d5)
  )
  (wire (pts (xy 87.63 101.6) (xy 87.63 109.22))
    (stroke (width 0) (type default))
    (uuid 6d8111fb-ba16-401d-821d-ce8149dc50b0)
  )
  (wire (pts (xy 88.9 109.22) (xy 91.44 109.22))
    (stroke (width 0) (type default))
    (uuid 6dbe10e9-e810-4a4e-9244-87231062c21c)
  )
  (wire (pts (xy 325.12 58.42) (xy 337.82 58.42))
    (stroke (width 0) (type default))
    (uuid 6fb0fa1e-7c20-4624-95fd-7c8752955296)
  )
  (wire (pts (xy 106.68 40.64) (xy 106.68 39.37))
    (stroke (width 0) (type default))
    (uuid 7029608b-4d5e-48a4-9cdd-19305a256ef7)
  )
  (polyline (pts (xy 373.38 24.13) (xy 373.38 176.53))
    (stroke (width 0) (type default))
    (uuid 706665ee-c3d8-4e49-97e5-b7dd4876ae50)
  )

  (wire (pts (xy 313.69 121.92) (xy 317.5 121.92))
    (stroke (width 0) (type default))
    (uuid 713f47dc-49c9-43da-8256-3215bd477535)
  )
  (wire (pts (xy 69.85 138.43) (xy 69.85 140.97))
    (stroke (width 0) (type default))
    (uuid 723be6f5-ee0b-4b8e-a3d6-883e792c73f3)
  )
  (wire (pts (xy 66.04 26.67) (xy 91.44 26.67))
    (stroke (width 0) (type default))
    (uuid 7275e875-44f5-496b-b18c-a6e7d8deb44a)
  )
  (wire (pts (xy 142.24 167.64) (xy 142.24 153.67))
    (stroke (width 0) (type default))
    (uuid 734433f5-d85b-47c7-b5ec-81db5c3a891d)
  )
  (wire (pts (xy 40.64 100.33) (xy 40.64 99.06))
    (stroke (width 0) (type default))
    (uuid 73f4256c-e95f-44ae-901e-1781afedac23)
  )
  (wire (pts (xy 364.49 168.91) (xy 356.87 168.91))
    (stroke (width 0) (type default))
    (uuid 73fb01fd-21e9-4895-bac4-b230c9301aae)
  )
  (wire (pts (xy 99.06 31.75) (xy 96.52 31.75))
    (stroke (width 0) (type default))
    (uuid 7512685f-591c-4c0f-9d32-5057583e186d)
  )
  (wire (pts (xy 350.52 39.37) (xy 350.52 48.26))
    (stroke (width 0) (type default))
    (uuid 7572934a-a87c-41cb-a1d6-eb92dc6aa1bb)
  )
  (wire (pts (xy 355.6 81.28) (xy 356.87 81.28))
    (stroke (width 0) (type default))
    (uuid 75f133ab-f8c4-449c-8d24-5ce2ef20c5f5)
  )
  (wire (pts (xy 372.11 132.08) (xy 379.73 132.08))
    (stroke (width 0) (type default))
    (uuid 777ed62e-d331-4279-bf29-d1397df7235b)
  )
  (wire (pts (xy 96.52 109.22) (xy 99.06 109.22))
    (stroke (width 0) (type default))
    (uuid 789ef0a4-200d-4a4a-b78e-3e6610a4c3b8)
  )
  (wire (pts (xy 306.07 119.38) (xy 317.5 119.38))
    (stroke (width 0) (type default))
    (uuid 7a4ed586-7d72-4ecd-aed4-3b2f85fa956d)
  )
  (wire (pts (xy 48.26 64.77) (xy 27.94 64.77))
    (stroke (width 0) (type default))
    (uuid 7a6cebdb-06e8-4146-912c-74ce4df7c05c)
  )
  (wire (pts (xy 66.04 24.13) (xy 66.04 26.67))
    (stroke (width 0) (type default))
    (uuid 7a6da99b-e380-4588-bf5c-08791fd58195)
  )
  (wire (pts (xy 45.72 36.83) (xy 45.72 39.37))
    (stroke (width 0) (type default))
    (uuid 7a706af9-6b87-4f98-8b3d-9e37db75faf4)
  )
  (wire (pts (xy 379.73 165.1) (xy 379.73 168.91))
    (stroke (width 0) (type default))
    (uuid 7ade595e-40fd-4fc7-a6f4-4677ca923248)
  )
  (wire (pts (xy 66.04 26.67) (xy 66.04 40.64))
    (stroke (width 0) (type default))
    (uuid 7b65b5dd-1d81-41a3-95e9-3803a0ac8865)
  )
  (wire (pts (xy 43.18 146.05) (xy 38.1 146.05))
    (stroke (width 0) (type default))
    (uuid 7ba91422-6cae-4989-8bac-0c174ff4d2cd)
  )
  (wire (pts (xy 92.71 217.17) (xy 92.71 215.9))
    (stroke (width 0) (type default))
    (uuid 7bf84d58-2812-409d-9266-83b176f6a91e)
  )
  (wire (pts (xy 139.7 210.82) (xy 167.64 210.82))
    (stroke (width 0) (type default))
    (uuid 7c2bdece-ed05-4226-a68c-e31b39910ad0)
  )
  (wire (pts (xy 43.18 24.13) (xy 58.42 24.13))
    (stroke (width 0) (type default))
    (uuid 7c807f90-5775-4aa6-98fc-68575047930f)
  )
  (wire (pts (xy 335.28 48.26) (xy 350.52 48.26))
    (stroke (width 0) (type default))
    (uuid 7cafa446-00e0-4641-9b9f-19c6ccc5577a)
  )
  (wire (pts (xy 115.57 152.4) (xy 129.54 152.4))
    (stroke (width 0) (type default))
    (uuid 7d187ab7-248a-4b44-8d65-88a20f2b870f)
  )
  (wire (pts (xy 135.89 124.46) (xy 137.16 124.46))
    (stroke (width 0) (type default))
    (uuid 7dacaff6-ecd0-4dcc-939c-23b7edd44113)
  )
  (wire (pts (xy 364.49 165.1) (xy 358.14 165.1))
    (stroke (width 0) (type default))
    (uuid 7ddaa77b-4380-4df1-9d25-b962089bd73b)
  )
  (wire (pts (xy 340.36 120.65) (xy 340.36 130.81))
    (stroke (width 0) (type default))
    (uuid 7dde8f77-c14b-43ce-bf7c-1f3c6d8ab010)
  )
  (wire (pts (xy 137.16 119.38) (xy 137.16 121.92))
    (stroke (width 0) (type default))
    (uuid 7edf0af6-250e-41bd-9700-e01bcbd3f0e6)
  )
  (wire (pts (xy 93.98 109.22) (xy 93.98 111.76))
    (stroke (width 0) (type default))
    (uuid 8054bc9a-135e-419d-bbb0-a28de12c89d5)
  )
  (polyline (pts (xy 289.56 176.53) (xy 289.56 24.13))
    (stroke (width 0) (type default))
    (uuid 8080e6f7-319e-4ef5-940d-3ce0f9f897d9)
  )

  (wire (pts (xy 104.14 219.71) (xy 104.14 217.17))
    (stroke (width 0) (type default))
    (uuid 8114ef43-71ac-4c97-8f1b-ffb6f2a17e6c)
  )
  (wire (pts (xy 58.42 124.46) (xy 64.77 124.46))
    (stroke (width 0) (type default))
    (uuid 8245a293-e149-4e2b-8d16-d396c0bbb419)
  )
  (wire (pts (xy 351.79 73.66) (xy 351.79 82.55))
    (stroke (width 0) (type default))
    (uuid 831983ef-0b90-4d2b-bbf6-4e009363b569)
  )
  (wire (pts (xy 379.73 91.44) (xy 372.11 91.44))
    (stroke (width 0) (type default))
    (uuid 834bc23b-519f-4fea-9243-456c1a55d3ea)
  )
  (wire (pts (xy 74.93 107.95) (xy 74.93 109.22))
    (stroke (width 0) (type default))
    (uuid 83bfe76a-4ac1-4fb6-92bb-3913143e25f2)
  )
  (wire (pts (xy 312.42 157.48) (xy 317.5 157.48))
    (stroke (width 0) (type default))
    (uuid 84b0ea06-2ffc-446d-8dc9-79f1b7dccc99)
  )
  (wire (pts (xy 140.97 148.59) (xy 140.97 147.32))
    (stroke (width 0) (type default))
    (uuid 84bb42a6-6176-4c76-a0c3-e12c5dd0394c)
  )
  (wire (pts (xy 356.87 154.94) (xy 344.17 154.94))
    (stroke (width 0) (type default))
    (uuid 8500aa77-b9d9-45aa-ba30-00f7b1313f1a)
  )
  (wire (pts (xy 138.43 193.04) (xy 144.78 193.04))
    (stroke (width 0) (type default))
    (uuid 85579de8-2097-4e00-8fcf-48fcca9f633a)
  )
  (wire (pts (xy 115.57 149.86) (xy 119.38 149.86))
    (stroke (width 0) (type default))
    (uuid 87de14c9-bf3b-4f03-b5f9-a6d72cb44b0a)
  )
  (wire (pts (xy 356.87 132.08) (xy 356.87 127))
    (stroke (width 0) (type default))
    (uuid 8891483b-d3f6-4af9-b4ff-be167e95bdde)
  )
  (wire (pts (xy 358.14 149.86) (xy 358.14 157.48))
    (stroke (width 0) (type default))
    (uuid 8a6bf03f-0abf-40af-a3af-6b680e98ce67)
  )
  (wire (pts (xy 142.24 129.54) (xy 144.78 129.54))
    (stroke (width 0) (type default))
    (uuid 8a8f9654-803c-43ec-9bdd-2b5e8c424be2)
  )
  (wire (pts (xy 345.44 50.8) (xy 337.82 50.8))
    (stroke (width 0) (type default))
    (uuid 8b0f7c05-df3e-47c7-9a11-0236c34d805b)
  )
  (wire (pts (xy 38.1 146.05) (xy 38.1 147.32))
    (stroke (width 0) (type default))
    (uuid 8b2200d1-b3fc-4910-92ee-accb9fc3a7ca)
  )
  (wire (pts (xy 93.98 101.6) (xy 93.98 109.22))
    (stroke (width 0) (type default))
    (uuid 8b8ba5fc-510d-42ec-a980-0deda8508332)
  )
  (wire (pts (xy 119.38 29.21) (xy 119.38 27.94))
    (stroke (width 0) (type default))
    (uuid 8ca6aa9b-6d68-4a74-b43f-855e57c02d12)
  )
  (wire (pts (xy 358.14 157.48) (xy 358.14 160.02))
    (stroke (width 0) (type default))
    (uuid 8ce3fa4a-26ea-4c17-9d51-933da7d19869)
  )
  (wire (pts (xy 91.44 40.64) (xy 106.68 40.64))
    (stroke (width 0) (type default))
    (uuid 8d090dc0-e007-480a-ade6-39be01648968)
  )
  (wire (pts (xy 353.06 57.15) (xy 353.06 50.8))
    (stroke (width 0) (type default))
    (uuid 8d878de7-1253-4abe-b0ad-b921b35c14e8)
  )
  (wire (pts (xy 144.78 193.04) (xy 144.78 205.74))
    (stroke (width 0) (type default))
    (uuid 8d98d59a-2455-4097-8d6e-bf92c9810551)
  )
  (wire (pts (xy 358.14 123.19) (xy 355.6 123.19))
    (stroke (width 0) (type default))
    (uuid 8da0ae28-f24d-4744-9961-c91009069c29)
  )
  (wire (pts (xy 43.18 29.21) (xy 55.88 29.21))
    (stroke (width 0) (type default))
    (uuid 8dc70da2-c72d-4aed-b65e-dfbcbd7d4938)
  )
  (wire (pts (xy 340.36 120.65) (xy 358.14 120.65))
    (stroke (width 0) (type default))
    (uuid 8e2aa638-b11f-475c-93c6-4d722acce202)
  )
  (wire (pts (xy 358.14 138.43) (xy 358.14 142.24))
    (stroke (width 0) (type default))
    (uuid 8e3a147c-a181-495d-a4ee-4fbc2f15c96e)
  )
  (wire (pts (xy 379.73 57.15) (xy 370.84 57.15))
    (stroke (width 0) (type default))
    (uuid 8e5601da-8378-46aa-b6c7-c83669c49809)
  )
  (wire (pts (xy 364.49 91.44) (xy 353.06 91.44))
    (stroke (width 0) (type default))
    (uuid 8fe78eb1-d06c-40ab-b2fd-d4e1d09aadae)
  )
  (wire (pts (xy 316.23 60.96) (xy 316.23 54.61))
    (stroke (width 0) (type default))
    (uuid 91312f52-f777-427d-8e79-c348d88d3931)
  )
  (wire (pts (xy 119.38 29.21) (xy 119.38 31.75))
    (stroke (width 0) (type default))
    (uuid 91c9bba1-9c7e-4857-9dea-f5bab1abbe2e)
  )
  (wire (pts (xy 106.68 91.44) (xy 113.03 91.44))
    (stroke (width 0) (type default))
    (uuid 92a7475b-96ba-4ed6-b66e-585238ef718b)
  )
  (wire (pts (xy 347.98 130.81) (xy 340.36 130.81))
    (stroke (width 0) (type default))
    (uuid 93a4df0f-e0e1-4f94-bb55-730dcd3d3e07)
  )
  (wire (pts (xy 66.04 40.64) (xy 73.66 40.64))
    (stroke (width 0) (type default))
    (uuid 93e8b934-7492-4850-b101-a6bfb4406cca)
  )
  (wire (pts (xy 344.17 154.94) (xy 337.82 154.94))
    (stroke (width 0) (type default))
    (uuid 93eaf25a-487e-4bc5-8525-09c061c6e0ea)
  )
  (wire (pts (xy 119.38 101.6) (xy 119.38 109.22))
    (stroke (width 0) (type default))
    (uuid 95eaa164-b2f2-456a-b4e0-5b30a4ba949f)
  )
  (wire (pts (xy 337.82 85.09) (xy 337.82 92.71))
    (stroke (width 0) (type default))
    (uuid 977a698d-9b0d-413b-af02-d577f47c11d2)
  )
  (wire (pts (xy 74.93 109.22) (xy 81.28 109.22))
    (stroke (width 0) (type default))
    (uuid 97c19e08-f0ec-4f8d-b649-c4df24a26cf6)
  )
  (wire (pts (xy 50.8 138.43) (xy 54.61 138.43))
    (stroke (width 0) (type default))
    (uuid 97f54e65-6ffe-4dbb-8897-4569dbef39d3)
  )
  (wire (pts (xy 379.73 38.1) (xy 379.73 53.34))
    (stroke (width 0) (type default))
    (uuid 986814cf-c8b4-4834-808d-f53da31af08d)
  )
  (wire (pts (xy 139.7 198.12) (xy 139.7 210.82))
    (stroke (width 0) (type default))
    (uuid 9a92b343-d958-40a1-8406-94ed25b5d64c)
  )
  (wire (pts (xy 337.82 76.2) (xy 337.82 85.09))
    (stroke (width 0) (type default))
    (uuid 9affff9c-1375-415e-9a70-89e00790fa78)
  )
  (wire (pts (xy 88.9 109.22) (xy 88.9 111.76))
    (stroke (width 0) (type default))
    (uuid 9bd7efba-e040-4eb1-8589-d6906fdc6515)
  )
  (wire (pts (xy 325.12 30.48) (xy 355.6 30.48))
    (stroke (width 0) (type default))
    (uuid 9be903f5-28a0-46c9-880d-290f994b89b2)
  )
  (wire (pts (xy 356.87 125.73) (xy 356.87 127))
    (stroke (width 0) (type default))
    (uuid 9be9925d-b65b-41bb-b7ba-921c4478aba6)
  )
  (wire (pts (xy 318.77 142.24) (xy 318.77 138.43))
    (stroke (width 0) (type default))
    (uuid 9c630b35-6e13-4def-88d2-a06907c6e02c)
  )
  (wire (pts (xy 69.85 146.05) (xy 54.61 146.05))
    (stroke (width 0) (type default))
    (uuid 9d8b2d41-68dd-49d9-92e2-33abad6de535)
  )
  (wire (pts (xy 25.4 109.22) (xy 24.13 109.22))
    (stroke (width 0) (type default))
    (uuid 9e889696-f0fb-4324-aa31-12dafd8075f3)
  )
  (wire (pts (xy 74.93 91.44) (xy 81.28 91.44))
    (stroke (width 0) (type default))
    (uuid 9f9d6318-036e-4112-8487-37a159967c92)
  )
  (wire (pts (xy 372.11 165.1) (xy 379.73 165.1))
    (stroke (width 0) (type default))
    (uuid 9fa24a08-c99f-4e30-ba16-67b5ff54c49f)
  )
  (wire (pts (xy 351.79 127) (xy 356.87 127))
    (stroke (width 0) (type default))
    (uuid 9fccacbf-5f6d-4889-944e-2812a1d52caf)
  )
  (wire (pts (xy 306.07 121.92) (xy 313.69 121.92))
    (stroke (width 0) (type default))
    (uuid a001a981-fc4a-4eb5-a384-144f24066239)
  )
  (wire (pts (xy 325.12 92.71) (xy 337.82 92.71))
    (stroke (width 0) (type default))
    (uuid a0565afb-b1ce-4e22-b89c-a8d4e4573da8)
  )
  (wire (pts (xy 312.42 77.47) (xy 314.96 77.47))
    (stroke (width 0) (type default))
    (uuid a058aa6b-41f3-467a-a211-bd8034113076)
  )
  (wire (pts (xy 307.34 77.47) (xy 312.42 77.47))
    (stroke (width 0) (type default))
    (uuid a0aecd25-d3f1-47a0-a368-2d316ae238ad)
  )
  (wire (pts (xy 115.57 121.92) (xy 120.65 121.92))
    (stroke (width 0) (type default))
    (uuid a0c99c7c-853a-455e-8069-02c5dde4bcfd)
  )
  (wire (pts (xy 355.6 46.99) (xy 356.87 46.99))
    (stroke (width 0) (type default))
    (uuid a0d80e29-458d-4fd2-a9c7-c73711de1757)
  )
  (wire (pts (xy 356.87 168.91) (xy 356.87 163.83))
    (stroke (width 0) (type default))
    (uuid a164c996-17e4-4959-9a33-939729370f90)
  )
  (wire (pts (xy 139.7 191.77) (xy 146.05 191.77))
    (stroke (width 0) (type default))
    (uuid a1aff6d2-7be0-4bf4-8627-e8f11222e892)
  )
  (wire (pts (xy 342.9 44.45) (xy 335.28 44.45))
    (stroke (width 0) (type default))
    (uuid a2e58f44-547f-43ab-99c2-adaff57361c0)
  )
  (wire (pts (xy 372.11 135.89) (xy 379.73 135.89))
    (stroke (width 0) (type default))
    (uuid a30ae75a-e7a4-4bf7-b5d4-87dd6875e30e)
  )
  (wire (pts (xy 336.55 74.93) (xy 336.55 78.74))
    (stroke (width 0) (type default))
    (uuid a4692562-d19d-4fa6-8c04-03929029b935)
  )
  (wire (pts (xy 40.64 109.22) (xy 43.18 109.22))
    (stroke (width 0) (type default))
    (uuid a57591f8-7e67-4742-9c54-b6fb6f8abe10)
  )
  (wire (pts (xy 130.81 151.13) (xy 146.05 151.13))
    (stroke (width 0) (type default))
    (uuid a6b7cb14-d68a-4e9d-be2f-071f08cd1abc)
  )
  (wire (pts (xy 115.57 167.64) (xy 138.43 167.64))
    (stroke (width 0) (type default))
    (uuid a755905d-01a8-4718-977b-4f6a1b07ed1c)
  )
  (wire (pts (xy 350.52 48.26) (xy 355.6 48.26))
    (stroke (width 0) (type default))
    (uuid a7a64db6-bafa-49f9-9cec-4bb8a6bc9f64)
  )
  (wire (pts (xy 58.42 132.08) (xy 38.1 132.08))
    (stroke (width 0) (type default))
    (uuid a84020df-f171-482a-9562-2ef9e78a1ea5)
  )
  (wire (pts (xy 337.82 127) (xy 351.79 127))
    (stroke (width 0) (type default))
    (uuid a8d55e6f-9a4d-4783-9902-bd6d183f723c)
  )
  (wire (pts (xy 43.18 39.37) (xy 45.72 39.37))
    (stroke (width 0) (type default))
    (uuid a96cfe06-fd24-4324-84d5-d9f73f28dc5a)
  )
  (wire (pts (xy 54.61 138.43) (xy 69.85 138.43))
    (stroke (width 0) (type default))
    (uuid abf147fd-e34a-431f-868a-fa2d2d7d0fee)
  )
  (wire (pts (xy 364.49 132.08) (xy 356.87 132.08))
    (stroke (width 0) (type default))
    (uuid ac6d9683-0c6f-42f2-bc18-9dec82b22a6e)
  )
  (wire (pts (xy 64.77 49.53) (xy 74.93 49.53))
    (stroke (width 0) (type default))
    (uuid ad215c65-1bfc-4cdf-8f02-a8cba3a0ad3a)
  )
  (wire (pts (xy 91.44 39.37) (xy 91.44 40.64))
    (stroke (width 0) (type default))
    (uuid ad2a67a7-b2ed-40c6-b42b-3eb17b2489ac)
  )
  (wire (pts (xy 64.77 46.99) (xy 83.82 46.99))
    (stroke (width 0) (type default))
    (uuid ad55b6eb-128c-47ce-9ae1-3d1e668d18ed)
  )
  (wire (pts (xy 379.73 132.08) (xy 379.73 135.89))
    (stroke (width 0) (type default))
    (uuid af0c0232-64d2-4d64-b729-0dc165c7597a)
  )
  (wire (pts (xy 337.82 154.94) (xy 337.82 152.4))
    (stroke (width 0) (type default))
    (uuid af670e12-b589-4f48-a8f9-c53151d09e44)
  )
  (wire (pts (xy 157.48 138.43) (xy 157.48 129.54))
    (stroke (width 0) (type default))
    (uuid af6f10e5-27b2-41a6-aed6-1595938cef40)
  )
  (wire (pts (xy 340.36 152.4) (xy 340.36 163.83))
    (stroke (width 0) (type default))
    (uuid afd0bfd7-ecd7-49de-821c-8400ef384796)
  )
  (wire (pts (xy 113.03 91.44) (xy 113.03 93.98))
    (stroke (width 0) (type default))
    (uuid b0b6df73-8dd3-454e-8565-9a98f37b1a1e)
  )
  (wire (pts (xy 95.25 217.17) (xy 95.25 215.9))
    (stroke (width 0) (type default))
    (uuid b130cdaf-6e18-4958-9a71-1ad662733d51)
  )
  (wire (pts (xy 316.23 54.61) (xy 293.37 54.61))
    (stroke (width 0) (type default))
    (uuid b1b89a60-0c59-4dcb-ba04-ae86542f2224)
  )
  (wire (pts (xy 138.43 200.66) (xy 138.43 213.36))
    (stroke (width 0) (type default))
    (uuid b1c3aac5-5d65-46e8-b95b-73dfc4d186af)
  )
  (wire (pts (xy 129.54 152.4) (xy 129.54 156.21))
    (stroke (width 0) (type default))
    (uuid b3dd4255-8a89-4996-98a3-37fbe2874e44)
  )
  (wire (pts (xy 335.28 82.55) (xy 351.79 82.55))
    (stroke (width 0) (type default))
    (uuid b47d0e98-9ab5-48ac-8dd1-6d845c27b7eb)
  )
  (wire (pts (xy 327.66 142.24) (xy 318.77 142.24))
    (stroke (width 0) (type default))
    (uuid b49a0377-4ee2-4a64-b806-dbf66ec3bcc2)
  )
  (wire (pts (xy 318.77 138.43) (xy 358.14 138.43))
    (stroke (width 0) (type default))
    (uuid b4cc8424-75d2-45cf-b9dc-ceb51717afe2)
  )
  (wire (pts (xy 142.24 153.67) (xy 146.05 153.67))
    (stroke (width 0) (type default))
    (uuid b5fd8693-c0e1-493b-93c5-6d06acd03976)
  )
  (wire (pts (xy 353.06 78.74) (xy 353.06 85.09))
    (stroke (width 0) (type default))
    (uuid b7f5957d-4252-4f62-81bb-cdb68c85bc58)
  )
  (wire (pts (xy 91.44 109.22) (xy 93.98 109.22))
    (stroke (width 0) (type default))
    (uuid b88225c5-2745-434c-a581-a727ed8480d8)
  )
  (wire (pts (xy 20.32 64.77) (xy 20.32 62.23))
    (stroke (width 0) (type default))
    (uuid bb2eb10e-839a-4abf-81e7-c35194344f1f)
  )
  (wire (pts (xy 353.06 44.45) (xy 342.9 44.45))
    (stroke (width 0) (type default))
    (uuid bd18b654-e5a1-494f-b14d-23881daf9a8f)
  )
  (wire (pts (xy 355.6 81.28) (xy 355.6 72.39))
    (stroke (width 0) (type default))
    (uuid bd419800-233c-4614-a066-a739e060fa63)
  )
  (wire (pts (xy 351.79 149.86) (xy 351.79 160.02))
    (stroke (width 0) (type default))
    (uuid be230970-1eb4-4c5e-92ae-e0e3506f4a7b)
  )
  (wire (pts (xy 349.25 163.83) (xy 340.36 163.83))
    (stroke (width 0) (type default))
    (uuid be4d63ee-749a-4aca-9c5e-7486656e47a9)
  )
  (wire (pts (xy 356.87 41.91) (xy 356.87 38.1))
    (stroke (width 0) (type default))
    (uuid c0f5208a-80d6-45d9-ad44-4a8f881cecaf)
  )
  (wire (pts (xy 139.7 165.1) (xy 139.7 191.77))
    (stroke (width 0) (type default))
    (uuid c18c9673-8034-483b-9cf5-d24bb787c710)
  )
  (wire (pts (xy 379.73 91.44) (xy 379.73 116.84))
    (stroke (width 0) (type default))
    (uuid c2c12440-df24-4787-82c1-306809854a98)
  )
  (wire (pts (xy 158.75 200.66) (xy 158.75 196.85))
    (stroke (width 0) (type default))
    (uuid c3330d13-83f7-45fe-8ded-d5e8eca21364)
  )
  (wire (pts (xy 34.29 99.06) (xy 34.29 109.22))
    (stroke (width 0) (type default))
    (uuid c5be2654-0a95-4347-b4de-83b4c038e92b)
  )
  (wire (pts (xy 293.37 30.48) (xy 325.12 30.48))
    (stroke (width 0) (type default))
    (uuid c7b96602-605d-4d14-ae01-3155e4d347e4)
  )
  (wire (pts (xy 137.16 121.92) (xy 137.16 124.46))
    (stroke (width 0) (type default))
    (uuid c97fe830-638e-488f-b9ae-f29d1bf7891b)
  )
  (wire (pts (xy 90.17 217.17) (xy 87.63 217.17))
    (stroke (width 0) (type default))
    (uuid c9f94645-4d60-4ad4-b313-45216a860369)
  )
  (wire (pts (xy 106.68 91.44) (xy 106.68 93.98))
    (stroke (width 0) (type default))
    (uuid ca5ea09f-f6b9-4649-a5e3-2d415f5260cb)
  )
  (wire (pts (xy 342.9 39.37) (xy 342.9 44.45))
    (stroke (width 0) (type default))
    (uuid ca9b1a24-1395-4127-8c25-81ec17e3a211)
  )
  (wire (pts (xy 97.79 217.17) (xy 95.25 217.17))
    (stroke (width 0) (type default))
    (uuid cacb9582-7577-47f5-96b1-5aec6507f3ec)
  )
  (wire (pts (xy 337.82 41.91) (xy 356.87 41.91))
    (stroke (width 0) (type default))
    (uuid cbf5f243-0e0b-4c49-b6db-43335f7007c4)
  )
  (wire (pts (xy 351.79 118.11) (xy 351.79 127))
    (stroke (width 0) (type default))
    (uuid cc93822a-5801-4890-ae48-e17284735d4e)
  )
  (wire (pts (xy 115.57 147.32) (xy 119.38 147.32))
    (stroke (width 0) (type default))
    (uuid cd6c8a88-0002-4d78-9dde-5ab0e2a93703)
  )
  (wire (pts (xy 167.64 200.66) (xy 158.75 200.66))
    (stroke (width 0) (type default))
    (uuid cdda6e97-4076-40e4-a039-4a808fb71268)
  )
  (wire (pts (xy 317.5 154.94) (xy 312.42 154.94))
    (stroke (width 0) (type default))
    (uuid ce7e7825-a68f-4214-ac7e-7a44668a5743)
  )
  (wire (pts (xy 43.18 41.91) (xy 45.72 41.91))
    (stroke (width 0) (type default))
    (uuid cebd63b5-a800-47d0-a038-e4c9df28db9c)
  )
  (wire (pts (xy 355.6 130.81) (xy 355.6 123.19))
    (stroke (width 0) (type default))
    (uuid cff83d91-c0fc-4259-8879-b47cc4fad557)
  )
  (wire (pts (xy 307.34 82.55) (xy 314.96 82.55))
    (stroke (width 0) (type default))
    (uuid d07adb8a-ca7b-4408-9bed-95738de30d7e)
  )
  (wire (pts (xy 63.5 41.91) (xy 63.5 43.18))
    (stroke (width 0) (type default))
    (uuid d0a8aef2-c2d9-4868-a2be-bc92f2625b4d)
  )
  (wire (pts (xy 335.28 74.93) (xy 336.55 74.93))
    (stroke (width 0) (type default))
    (uuid d0ad3752-1409-4e24-8849-3514abf1a71c)
  )
  (wire (pts (xy 135.89 119.38) (xy 137.16 119.38))
    (stroke (width 0) (type default))
    (uuid d0ee70dd-5b8e-438b-a3f1-34329195534a)
  )
  (wire (pts (xy 160.02 124.46) (xy 160.02 129.54))
    (stroke (width 0) (type default))
    (uuid d13d2d19-b8a5-4302-b20d-c5c7b487304c)
  )
  (wire (pts (xy 142.24 138.43) (xy 142.24 132.08))
    (stroke (width 0) (type default))
    (uuid d1467f05-b918-4d12-9761-995e30347926)
  )
  (wire (pts (xy 160.02 129.54) (xy 157.48 129.54))
    (stroke (width 0) (type default))
    (uuid d17e6025-998f-4d6f-a2b9-6d7d8cae984b)
  )
  (wire (pts (xy 340.36 163.83) (xy 340.36 170.18))
    (stroke (width 0) (type default))
    (uuid d1fec454-d539-44f7-b251-26e58cf7f659)
  )
  (polyline (pts (xy 373.38 176.53) (xy 289.56 176.53))
    (stroke (width 0) (type default))
    (uuid d2551795-8d48-49be-9924-a33a0e18e1d7)
  )

  (wire (pts (xy 106.68 101.6) (xy 106.68 109.22))
    (stroke (width 0) (type default))
    (uuid d2726674-e7d3-4981-82ca-4b2f3253cd92)
  )
  (wire (pts (xy 40.64 107.95) (xy 40.64 109.22))
    (stroke (width 0) (type default))
    (uuid d3223cd2-67bd-42be-a9c5-d43be223fd55)
  )
  (wire (pts (xy 45.72 44.45) (xy 45.72 41.91))
    (stroke (width 0) (type default))
    (uuid d3b491e5-e177-4f3f-a540-2c674405bfe7)
  )
  (wire (pts (xy 63.5 43.18) (xy 73.66 43.18))
    (stroke (width 0) (type default))
    (uuid d59a0234-9408-4a49-9a47-e6023d21ebc9)
  )
  (wire (pts (xy 96.52 31.75) (xy 96.52 29.21))
    (stroke (width 0) (type default))
    (uuid d5be2ce4-3500-47fe-b572-783339b0f9eb)
  )
  (wire (pts (xy 327.66 109.22) (xy 356.87 109.22))
    (stroke (width 0) (type default))
    (uuid d5da943f-70aa-4fa0-8e65-231e5d0db89a)
  )
  (wire (pts (xy 83.82 43.18) (xy 83.82 46.99))
    (stroke (width 0) (type default))
    (uuid d7432553-ae3f-4f2f-97c1-ec827286ef58)
  )
  (wire (pts (xy 312.42 154.94) (xy 312.42 157.48))
    (stroke (width 0) (type default))
    (uuid d7adb1cd-ecea-4df1-9441-4bc4526c7ffc)
  )
  (wire (pts (xy 356.87 78.74) (xy 353.06 78.74))
    (stroke (width 0) (type default))
    (uuid d9343220-adb7-493e-8512-acd433fce295)
  )
  (wire (pts (xy 63.5 52.07) (xy 74.93 52.07))
    (stroke (width 0) (type default))
    (uuid d94785e7-ba6b-4a6c-979e-fd80d08e3e10)
  )
  (wire (pts (xy 135.89 121.92) (xy 137.16 121.92))
    (stroke (width 0) (type default))
    (uuid d949c731-08a0-4fde-8d42-bee8d9d7d3ba)
  )
  (wire (pts (xy 356.87 125.73) (xy 358.14 125.73))
    (stroke (width 0) (type default))
    (uuid dd0f951e-ba63-4b00-bf12-04c434badfc9)
  )
  (wire (pts (xy 327.66 109.22) (xy 293.37 109.22))
    (stroke (width 0) (type default))
    (uuid dd57dfdf-e80c-4113-a096-6da79eb8c63f)
  )
  (wire (pts (xy 137.16 127) (xy 135.89 127))
    (stroke (width 0) (type default))
    (uuid de2fe51b-bbf2-487b-8b9c-fefefafb1641)
  )
  (wire (pts (xy 91.44 109.22) (xy 91.44 111.76))
    (stroke (width 0) (type default))
    (uuid de8ba2f8-eec0-44ca-9c7d-79225b66fba7)
  )
  (wire (pts (xy 314.96 45.72) (xy 311.15 45.72))
    (stroke (width 0) (type default))
    (uuid df0e9e15-0825-40db-b528-500b352ac099)
  )
  (wire (pts (xy 306.07 154.94) (xy 312.42 154.94))
    (stroke (width 0) (type default))
    (uuid df4ea54c-b2b4-4985-9664-25ece359cf7f)
  )
  (wire (pts (xy 344.17 149.86) (xy 344.17 154.94))
    (stroke (width 0) (type default))
    (uuid dfbc8a74-ec65-4100-8e1d-af05e890cf6d)
  )
  (wire (pts (xy 345.44 85.09) (xy 337.82 85.09))
    (stroke (width 0) (type default))
    (uuid e1ba2435-9071-473a-812a-ba3acdd83c1b)
  )
  (wire (pts (xy 97.79 217.17) (xy 97.79 215.9))
    (stroke (width 0) (type default))
    (uuid e2873e3a-93fe-4e99-bb63-6d645a7f6579)
  )
  (wire (pts (xy 100.33 91.44) (xy 106.68 91.44))
    (stroke (width 0) (type default))
    (uuid e3e2211a-0ffd-4c3c-a583-5bbc586c5560)
  )
  (wire (pts (xy 81.28 109.22) (xy 87.63 109.22))
    (stroke (width 0) (type default))
    (uuid e4207225-1f1f-44d7-a7c0-bcb2f12cbf6a)
  )
  (wire (pts (xy 115.57 132.08) (xy 142.24 132.08))
    (stroke (width 0) (type default))
    (uuid e46a8c8c-beb0-4304-9049-1784ea27779f)
  )
  (wire (pts (xy 115.57 182.88) (xy 146.05 182.88))
    (stroke (width 0) (type default))
    (uuid e4aa454d-5ee5-4d35-8ed6-fe2247aaaa46)
  )
  (wire (pts (xy 87.63 217.17) (xy 87.63 215.9))
    (stroke (width 0) (type default))
    (uuid e4c1acc0-5c5b-4ed7-a35d-4001ad1a8588)
  )
  (wire (pts (xy 129.54 156.21) (xy 146.05 156.21))
    (stroke (width 0) (type default))
    (uuid e4fb84b8-40ad-4321-8d34-60ecd21dd1eb)
  )
  (wire (pts (xy 92.71 217.17) (xy 90.17 217.17))
    (stroke (width 0) (type default))
    (uuid e502ce44-4b73-4aaa-840b-e34301f357c7)
  )
  (wire (pts (xy 48.26 31.75) (xy 43.18 31.75))
    (stroke (width 0) (type default))
    (uuid e6f7d9e6-d5cd-4576-a093-d0c945065233)
  )
  (wire (pts (xy 379.73 116.84) (xy 379.73 132.08))
    (stroke (width 0) (type default))
    (uuid e973c23f-4566-4983-a072-7528850b5b63)
  )
  (wire (pts (xy 356.87 163.83) (xy 356.87 154.94))
    (stroke (width 0) (type default))
    (uuid e9f7bb74-941a-4ee3-bcaf-15a2a753227a)
  )
  (wire (pts (xy 90.17 217.17) (xy 90.17 215.9))
    (stroke (width 0) (type default))
    (uuid eb68919c-85cb-4bac-97cf-bf818c5e0542)
  )
  (wire (pts (xy 55.88 29.21) (xy 55.88 46.99))
    (stroke (width 0) (type default))
    (uuid eb6ad5c3-77fa-4d70-8fc0-5ee0713bf97a)
  )
  (wire (pts (xy 306.07 127) (xy 317.5 127))
    (stroke (width 0) (type default))
    (uuid edae2f39-9b88-430a-a167-17c5b7a996f3)
  )
  (wire (pts (xy 91.44 31.75) (xy 91.44 29.21))
    (stroke (width 0) (type default))
    (uuid edc99fa6-0ad1-44a7-bad2-96440c26a47e)
  )
  (wire (pts (xy 58.42 24.13) (xy 66.04 24.13))
    (stroke (width 0) (type default))
    (uuid ee2b97bf-9f86-485a-ae9d-191e2b6d3dd4)
  )
  (wire (pts (xy 119.38 40.64) (xy 106.68 40.64))
    (stroke (width 0) (type default))
    (uuid ee315564-1e89-4692-803e-14dbb1bb73a9)
  )
  (wire (pts (xy 95.25 217.17) (xy 92.71 217.17))
    (stroke (width 0) (type default))
    (uuid ef5b51f6-b5d7-4199-8c1f-5674a5111de7)
  )
  (wire (pts (xy 317.5 124.46) (xy 313.69 124.46))
    (stroke (width 0) (type default))
    (uuid efaa72e2-365a-4d60-87fe-2f35c471bc5a)
  )
  (wire (pts (xy 311.15 43.18) (xy 314.96 43.18))
    (stroke (width 0) (type default))
    (uuid f0aa1af2-ebcf-4fb0-a86b-03fe5ad98f93)
  )
  (wire (pts (xy 64.77 39.37) (xy 64.77 46.99))
    (stroke (width 0) (type default))
    (uuid f1a10d50-c3f1-4c1e-bb45-cd11dd27df2e)
  )
  (wire (pts (xy 358.14 157.48) (xy 359.41 157.48))
    (stroke (width 0) (type default))
    (uuid f252a5a3-d54b-4027-826e-5384f8e26085)
  )
  (wire (pts (xy 48.26 54.61) (xy 48.26 64.77))
    (stroke (width 0) (type default))
    (uuid f27d0c59-8ba4-4342-804d-166471c59d78)
  )
  (wire (pts (xy 74.93 93.98) (xy 74.93 91.44))
    (stroke (width 0) (type default))
    (uuid f8252d86-0fbd-4671-b92f-9cd6c76a09f2)
  )
  (wire (pts (xy 157.48 129.54) (xy 154.94 129.54))
    (stroke (width 0) (type default))
    (uuid f9430c39-043a-4e07-a0db-a983f5fdd933)
  )
  (wire (pts (xy 355.6 82.55) (xy 355.6 81.28))
    (stroke (width 0) (type default))
    (uuid f943dc51-cfd2-4eef-be66-81ac36ce94d1)
  )
  (wire (pts (xy 24.13 109.22) (xy 24.13 99.06))
    (stroke (width 0) (type default))
    (uuid f98d71dd-3c1f-4c42-a700-60faf82e1b07)
  )
  (wire (pts (xy 55.88 54.61) (xy 55.88 64.77))
    (stroke (width 0) (type default))
    (uuid fa4393d3-dd60-48cf-a2a7-91c00b1f4073)
  )
  (wire (pts (xy 93.98 91.44) (xy 100.33 91.44))
    (stroke (width 0) (type default))
    (uuid fa97481a-5193-43cd-abe2-47e079d81deb)
  )
  (wire (pts (xy 358.14 116.84) (xy 379.73 116.84))
    (stroke (width 0) (type default))
    (uuid fc6a037c-6f7a-49a3-b09b-8b75b6d79b0a)
  )
  (wire (pts (xy 113.03 101.6) (xy 113.03 109.22))
    (stroke (width 0) (type default))
    (uuid fcec7d39-15e5-4e97-99e1-52120c04c9df)
  )
  (wire (pts (xy 359.41 148.59) (xy 379.73 148.59))
    (stroke (width 0) (type default))
    (uuid fcfaec9e-04e3-42f2-96bd-3a7e31baa906)
  )
  (wire (pts (xy 327.66 137.16) (xy 340.36 137.16))
    (stroke (width 0) (type default))
    (uuid fd95e6e1-2b37-4604-8f85-981efac5e15c)
  )
  (wire (pts (xy 43.18 36.83) (xy 45.72 36.83))
    (stroke (width 0) (type default))
    (uuid fe3ccf6c-3bc7-4352-a286-cbd22aac2e33)
  )
  (wire (pts (xy 55.88 64.77) (xy 48.26 64.77))
    (stroke (width 0) (type default))
    (uuid ffbeb38f-f8d8-4ab7-9e6e-19159aa2bb0b)
  )

  (text "I²C4" (at 58.42 179.07 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 1beded0d-60b1-409c-a4b1-0dd7ff55c0d5)
  )
  (text "USART1\nDMX1" (at 311.15 116.84 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 4a708683-f86a-4bbf-ac2d-ea63626c1f0c)
  )
  (text "50-100V max at ESD <15kV" (at 342.9 26.67 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 59c8383d-db8b-4797-81ba-132e61162e82)
  )
  (text "TEKO TEKAL 31.29\n(Or any Eurocard [100x100mm])\nCase"
    (at 123.19 27.94 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 5ba3a3ec-0693-4c12-b670-dc699cf4e3b7)
  )
  (text "USART3" (at 127 189.23 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 8f0259ab-6ea6-491d-86c2-ee38f801165b)
  )
  (text "UART4" (at 52.07 189.23 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid a6b320a6-df7d-447a-9144-7984dd85e78e)
  )
  (text "DMX4" (at 304.8 68.58 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid bb23a819-f910-41fb-89a6-9724f9dba25c)
  )
  (text "USART2" (at 127 171.45 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid de554f49-4d9e-4133-a34a-eba0abf2a486)
  )
  (text "DMX2" (at 308.61 147.32 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid eba1cf58-5b26-44c3-b0cb-a562158f44b9)
  )
  (text "DMX3" (at 304.8 34.29 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid f563d085-c444-4ed8-a281-fa370c930721)
  )
  (text "USART1" (at 127 179.07 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid fd73e8b2-cb36-443e-8dfc-e51d883e5da2)
  )

  (label "SWO" (at 139.7 161.29 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 0e68bf72-e737-4248-8c8a-6bb9fb931879)
  )
  (label "SWCLK" (at 139.7 151.13 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 736e1ac7-9191-4812-aad7-3dd365a3f9b7)
  )
  (label "SWDIO" (at 139.7 156.21 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 8f9fc1d0-48a7-4ac6-9a16-03d2f4836959)
  )
  (label "NRST" (at 139.7 158.75 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e1effc4d-a366-4cc5-add5-4d83b481e3c8)
  )

  (global_label "NRST" (shape input) (at 67.31 135.89 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 0646a32a-a286-4345-bb26-944f505cd6f3)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 60.2082 135.8106 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX1_TX" (shape input) (at 69.85 187.96 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 0a3b6cdd-9bff-4579-8f88-72426bffbc7d)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 59.2406 188.0394 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "USB-" (shape input) (at 74.93 49.53 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 0d1ccf5c-1126-4c6c-9547-d3d1309e3379)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 82.564 49.53 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX1_TX" (shape input) (at 307.34 48.26 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 151de907-8121-495b-9442-c0169f087804)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.7306 48.1806 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX2_RX" (shape input) (at 115.57 190.5 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 1df04a59-2cd5-4942-92d4-15d8a4026fba)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4818 190.4206 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "USB+" (shape input) (at 74.93 52.07 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 27ec7920-1e6c-49f9-8d4a-18f0728e7c12)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 82.564 52.07 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "SCL" (shape input) (at 69.85 177.8 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 3382bc3f-5d8d-4c59-89e9-22dc63a4e7bd)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 64.0908 177.8 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX2_DE" (shape input) (at 307.34 77.47 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 33d559b3-1800-4102-9147-7143fba1df82)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.4887 77.3906 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX4_RX" (shape input) (at 306.07 152.4 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 3887d27a-9821-416a-be47-6dc1a556a28a)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.1582 152.3206 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX3_RX" (shape input) (at 115.57 180.34 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 45c16f38-dfde-4975-a67e-f56fc51f71b5)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4818 180.4194 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX1_RX" (shape input) (at 69.85 190.5 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 4b12f8c7-dd21-4598-bae6-60a2532cf907)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 58.9382 190.5794 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX4_DE" (shape input) (at 306.07 154.94 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 5336034b-eda6-4117-99c7-16ac86c8dd5f)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.2187 154.8606 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "USB+" (shape input) (at 119.38 149.86 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 7015d0c6-5049-4614-8a5d-b1aa4c6490b0)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 0 20.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
  (global_label "DMX2_RX" (shape input) (at 307.34 74.93 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 73f90216-ec69-4d92-af77-c556c9bf6e04)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.4282 74.8506 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "SCL" (shape input) (at 167.64 195.58 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 77e82063-f6b6-44de-98a2-90c87ed7819b)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 161.8808 195.58 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX3_TX" (shape input) (at 306.07 127 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid a001c129-0183-4e3b-8c99-573f876ec419)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.4606 126.9206 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "NRST" (shape input) (at 43.18 109.22 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid a79595d0-84f9-4819-a174-9e36cb737215)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 50.2818 109.2994 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "USB-" (shape input) (at 119.38 147.32 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid af7df523-9938-4746-a447-cc4a6cef3aaf)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 0 20.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
  (global_label "DMX1_RX" (shape input) (at 307.34 40.64 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid b0d6b919-3360-4726-bd71-61f7ceef3608)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.4282 40.5606 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX1_DE" (shape input) (at 307.34 43.18 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid ba45cf49-7f7d-487a-9856-b700b3829a2c)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.4887 43.1006 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX2_DE" (shape input) (at 115.57 193.04 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid bad11c65-d839-4ed9-99b3-c36a8294ee33)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4213 192.9606 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX2_TX" (shape input) (at 115.57 187.96 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid bb5635b8-6648-4821-80f2-8bafb580822b)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.1794 187.8806 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX2_TX" (shape input) (at 307.34 82.55 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid c3bbb894-f2f6-406e-a5d4-2f63e74c0fbf)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 296.7306 82.4706 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX4_TX" (shape input) (at 115.57 170.18 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid c60ca025-0fb9-454e-8b7b-72c4cd4d1229)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.1794 170.1006 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX4_DE" (shape input) (at 69.85 193.04 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid cc36a8d3-74a4-4a80-b0c6-30e963aa5e3d)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 58.9987 193.1194 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "SDA" (shape input) (at 167.64 193.04 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid cf37e29f-ebe8-4a03-8c43-4447b0198a98)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 161.8203 193.04 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX1_DE" (shape input) (at 115.57 157.48 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid d2a129c7-6768-434c-b44a-a0220f092fea)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4213 157.4006 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX3_DE" (shape input) (at 115.57 175.26 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid d564e4de-b719-4c39-a149-026db2832ec4)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4213 175.3394 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX3_RX" (shape input) (at 306.07 119.38 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid e5b08369-288f-4d82-b186-1d721f297e58)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.1582 119.3006 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX3_DE" (shape input) (at 306.07 121.92 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid e787edce-0e12-44c2-8fe8-7fddf3cfa8ba)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.2187 121.8406 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "NRST" (shape input) (at 135.89 158.75 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid e8e615ce-8edd-48cf-b0af-26eeb1d240ef)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 128.7882 158.6706 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX4_RX" (shape input) (at 115.57 172.72 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid e9137ab0-a7e6-46e2-bc3a-ea81c95b5c70)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.4818 172.6406 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "DMX3_TX" (shape input) (at 115.57 177.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid ea589c7e-1e6a-4e25-8e6c-bdb6331b5d25)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 126.1794 177.8794 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "SDA" (shape input) (at 69.85 180.34 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid efe77869-675e-4a0a-ae2d-d978436b722a)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 64.0303 180.34 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )
  (global_label "DMX4_TX" (shape input) (at 306.07 160.02 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid fb220019-8ea3-47c3-a82c-442de2924d7f)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (at 295.4606 159.9406 0)
      (effects (font (size 1.27 1.27)) (justify right) hide)
    )
  )

  (symbol (lib_id "Interface_UART:MAX481E") (at 327.66 154.94 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4c7897)
    (property "Reference" "U7" (at 321.31 140.97 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MAX3440E" (at 334.01 142.24 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_SO:SOIC-8_3.9x4.9mm_P1.27mm" (at 327.66 172.72 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf" (at 327.66 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fb768c98-c000-4bb6-906e-5974b5d99ba9))
    (pin "2" (uuid aa05b342-8310-4485-b209-fa11793d97cf))
    (pin "3" (uuid 32c9f7eb-9207-407c-987d-84cdf500dcc9))
    (pin "4" (uuid 5d543600-9c31-4556-995a-1e31d1e6c297))
    (pin "5" (uuid 8b3c5225-c03b-4e32-90a8-e5bc03df8dd7))
    (pin "6" (uuid 6bb1d822-291c-4fc4-96a8-60da887b1ab2))
    (pin "7" (uuid 73d4f520-95c3-4e9b-a089-d5a0bc67d3dd))
    (pin "8" (uuid bc3468f8-cc0b-48a7-845f-eb20a4e28dd4))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U7") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Interface_UART:MAX481E") (at 327.66 121.92 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4c99a2)
    (property "Reference" "U8" (at 321.31 107.95 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MAX3440E" (at 334.01 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_SO:SOIC-8_3.9x4.9mm_P1.27mm" (at 327.66 139.7 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf" (at 327.66 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3397db1a-e0a9-44b6-bfa1-7c9717b022f2))
    (pin "2" (uuid 0bf217e0-a2d4-4170-8f1b-************))
    (pin "3" (uuid 0041d1d0-0769-439e-8a07-c0725233de85))
    (pin "4" (uuid be326377-c7fb-4a64-a759-59c73430549b))
    (pin "5" (uuid 63caafa2-5a3a-4be0-83c8-cdec9e116fe4))
    (pin "6" (uuid 00adb638-d264-47d9-83da-098abe900297))
    (pin "7" (uuid a956d9b4-ac97-4486-a85c-6da80c182a29))
    (pin "8" (uuid 42a97560-fe4b-494c-8827-845d292088a9))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U8") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Interface_UART:MAX481E") (at 325.12 77.47 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4cf9da)
    (property "Reference" "U5" (at 317.5 63.5 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MAX3440E" (at 331.47 64.77 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_SO:SOIC-8_3.9x4.9mm_P1.27mm" (at 325.12 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf" (at 325.12 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c500521a-2e25-409e-9141-1e4840f59638))
    (pin "2" (uuid a1adcb6c-db44-4b49-93cb-68ae942fb387))
    (pin "3" (uuid d049bfc9-cd69-4a38-8230-ce0eb7bd7f54))
    (pin "4" (uuid ad2723bb-ebb4-4134-bb74-9b733cf7e827))
    (pin "5" (uuid 3500cbcd-7c3a-47db-a543-88e32488371b))
    (pin "6" (uuid 9a01f491-431c-45d3-a6bc-46f48d97e8db))
    (pin "7" (uuid 1818e307-ae4b-484d-8cd6-9b4cd820b3cb))
    (pin "8" (uuid a5287c40-41de-41ac-9479-5d45543c04d8))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U5") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Interface_UART:MAX481E") (at 325.12 43.18 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4d012b)
    (property "Reference" "U6" (at 318.77 29.21 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MAX3440E" (at 332.74 31.75 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_SO:SOIC-8_3.9x4.9mm_P1.27mm" (at 325.12 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://datasheets.maximintegrated.com/en/ds/MAX1487E-MAX491E.pdf" (at 325.12 41.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid db8d59a2-716f-4209-8180-ab8b4531f54d))
    (pin "2" (uuid 60f79cd0-e782-41c1-bcab-f880d7c41159))
    (pin "3" (uuid 70874e64-f062-49e9-9991-cb8aa22fd1bd))
    (pin "4" (uuid 3da94e16-0ec3-4c07-bacd-33b311b92a6a))
    (pin "5" (uuid 087ff793-2e56-4fbf-b5f3-a7375b194b23))
    (pin "6" (uuid fa839e4e-68d3-48fe-837d-19bd49cf2eb1))
    (pin "7" (uuid 7d84f371-d1a6-4510-a6d1-38c157a30e48))
    (pin "8" (uuid 37945d7d-9f3c-40cb-b216-d11c580396ac))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U6") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Screw_Terminal_01x03") (at 364.49 154.94 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4edd68)
    (property "Reference" "J6" (at 366.522 153.8732 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "DMX" (at 366.522 156.1846 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm" (at 364.49 154.94 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 364.49 154.94 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1f13f2e7-fbb5-43c4-8a68-43c107b8d5af))
    (pin "2" (uuid dd70b6df-d559-40a7-bc3b-c233ac555e02))
    (pin "3" (uuid 6dbde7b2-5cc3-4f54-9fa7-8e9e342f5a44))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J6") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Screw_Terminal_01x03") (at 363.22 123.19 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d4eedb0)
    (property "Reference" "J5" (at 365.252 122.1232 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "DMX" (at 365.252 124.4346 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm" (at 363.22 123.19 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 363.22 123.19 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7f84d296-92fa-4728-8ad6-156fff978c95))
    (pin "2" (uuid 53250aa2-2e4b-4a74-853f-d3fe7864a7cd))
    (pin "3" (uuid 00ef5248-9373-4d84-a915-6242ecb8a3a2))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J5") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Screw_Terminal_01x03") (at 361.95 78.74 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d6b8cc6)
    (property "Reference" "J4" (at 363.982 77.6732 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "DMX" (at 363.982 79.9846 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm" (at 361.95 78.74 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 361.95 78.74 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e1cf11f2-417a-45fc-b8fe-149cc103edc8))
    (pin "2" (uuid d3cf16d8-186a-40ab-b972-aef71baa55f4))
    (pin "3" (uuid ff06d10c-7f99-44ad-8e59-f96f7050fd7b))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J4") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Screw_Terminal_01x03") (at 361.95 44.45 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d6bdb2f)
    (property "Reference" "J3" (at 363.982 43.3832 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "DMX" (at 363.982 45.6946 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "TerminalBlock:TerminalBlock_Altech_AK300-3_P5.00mm" (at 361.95 44.45 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 361.95 44.45 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3394b2b1-594a-42ad-ad3e-94ca3555c51c))
    (pin "2" (uuid e8e1c25a-c1bb-46a4-844e-dbdece1adc20))
    (pin "3" (uuid 35506fd2-d4e9-4a85-b670-85dfd25a7616))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 27.94 66.04 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d703bda)
    (property "Reference" "#PWR0109" (at 27.94 72.39 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 28.067 70.4342 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 27.94 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 27.94 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5b079484-c96a-47d8-b090-b2f79c5559ab))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0109") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+5V") (at 58.42 21.59 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d722b9d)
    (property "Reference" "#PWR0110" (at 58.42 25.4 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+5V" (at 58.801 17.1958 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 58.42 21.59 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 58.42 21.59 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e306ac99-151e-434f-bfe4-5602beef3a72))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0110") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 91.44 35.56 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7291c6)
    (property "Reference" "C1" (at 94.361 34.3916 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1μF" (at 94.361 36.703 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 92.4052 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 91.44 35.56 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5c77152c-51ac-423e-94f8-e9955b6f14b4))
    (pin "2" (uuid 3883deb4-2154-43b4-8b38-94d162c1144f))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 91.44 43.18 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d73254a)
    (property "Reference" "#PWR0111" (at 91.44 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 91.567 47.5742 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 91.44 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 91.44 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 55c066f8-f53d-42b8-8103-31968cbe2e8f))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0111") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 119.38 27.94 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d748cf6)
    (property "Reference" "#PWR0112" (at 119.38 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 119.761 23.5458 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 119.38 27.94 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 119.38 27.94 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 49982983-58f2-4ffb-bfe7-01c749826b63))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0112") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 81.28 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d74e1d5)
    (property "Reference" "C7" (at 81.28 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (at 81.28 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 82.2452 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 81.28 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e9752112-6a21-4d13-9dd3-f30f426282f3))
    (pin "2" (uuid b11d9828-8a5b-404b-997c-30bb2a40c848))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C7") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 87.63 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d74fad9)
    (property "Reference" "C8" (at 87.63 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (at 87.63 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 88.5952 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 87.63 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c95083e0-8ad3-4efb-9012-d2ed1b0c39de))
    (pin "2" (uuid 9e6634fa-556b-43b1-9a1a-620d3a150af5))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C8") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 93.98 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d75005e)
    (property "Reference" "C9" (at 93.98 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (at 93.98 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 94.9452 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 93.98 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 68679a2e-672e-42eb-bee8-eb1197072b47))
    (pin "2" (uuid af928832-39b0-4a3d-8073-87c48eddbad5))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C9") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 100.33 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d75037d)
    (property "Reference" "C10" (at 100.33 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (at 100.33 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 101.2952 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 100.33 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8bc95b20-36b5-4ec4-a68c-2edfb5079d13))
    (pin "2" (uuid 1a1c30a6-ddb1-476c-95e9-4081ef5b68e9))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C10") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 106.68 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d750507)
    (property "Reference" "C11" (at 106.68 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10nF" (at 106.68 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 107.6452 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 106.68 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 83063523-cdf2-42e9-8802-b6c19553f26e))
    (pin "2" (uuid f42a5b60-5d96-4544-a9dd-5a8d4e2bd6a3))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C11") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 113.03 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d750742)
    (property "Reference" "C12" (at 113.03 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "4.7μF" (at 113.03 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 113.9952 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 113.03 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fac20449-0ba4-4fac-8033-4a7400f1194f))
    (pin "2" (uuid 63d5ba9b-e677-482d-ab50-6664aa9a57ae))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C12") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 119.38 97.79 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d750964)
    (property "Reference" "C13" (at 119.38 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1μF" (at 119.38 100.33 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 120.3452 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 119.38 97.79 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0b85d9c8-cd3d-4745-ac19-4f5a413a62b4))
    (pin "2" (uuid ecf5848b-f7d5-4cd2-afdc-ef18e48b1bc2))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C13") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 74.93 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d76b2b4)
    (property "Reference" "#PWR0113" (at 74.93 111.76 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 75.311 103.5558 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 74.93 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 74.93 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 504a527c-6370-4e8c-a93c-a193e6eb17f1))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0113") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 40.64 104.14 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d79b662)
    (property "Reference" "R5" (at 42.418 102.9716 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "4K7" (at 42.418 105.283 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 38.862 104.14 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 40.64 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c336191f-b02f-4ffb-ad2f-d34bc14751cd))
    (pin "2" (uuid 2a22104a-7e7b-4444-9045-6612fb533ed6))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R5") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 24.13 109.22 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7a97b6)
    (property "Reference" "#PWR0114" (at 24.13 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 24.13 113.03 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 24.13 109.22 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 24.13 109.22 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c88a80e0-4c9f-4e28-8f1b-60d92611ae72))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0114") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_Push") (at 29.21 99.06 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7abd55)
    (property "Reference" "SW1" (at 29.21 91.821 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "SW_Push" (at 29.21 94.1324 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_PUSH_6mm" (at 29.21 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 29.21 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3e04fb9a-0cea-46de-a1ab-74781d946779))
    (pin "2" (uuid 2508eed2-92d2-4b58-851b-8ae18c788cc3))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "SW1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 29.21 109.22 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7ae069)
    (property "Reference" "C4" (at 29.21 102.8192 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "100nF" (at 29.21 105.1306 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 25.4 110.1852 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 29.21 109.22 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e921f6c6-b774-459d-8ac6-1c060bd8609a))
    (pin "2" (uuid 2fd9df31-1712-4190-8f09-4fd908fe49e0))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C4") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 74.93 93.98 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7c7f3a)
    (property "Reference" "#PWR0124" (at 74.93 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 75.057 98.3742 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 74.93 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 74.93 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 09770fbb-4f13-47ad-93ed-b13fc136a154))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0124") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Jumper:Jumper_3_Bridged12") (at 149.86 182.88 270) (mirror x) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7d2aab)
    (property "Reference" "JP1" (at 151.5618 181.7116 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "Jumper_3_Bridged12" (at 151.5618 184.023 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical" (at 149.86 182.88 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 149.86 182.88 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1f5d9648-6016-4437-b2eb-b2b07e22aca3))
    (pin "2" (uuid 09e9a941-ca67-49b4-916b-161c818a0546))
    (pin "3" (uuid 25ca2d76-abdf-43b7-81b3-6e2ecd45b842))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "JP1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 149.86 176.53 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d7e32e2)
    (property "Reference" "#PWR0115" (at 149.86 180.34 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 149.479 172.1358 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 149.86 176.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 149.86 176.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7ae7819e-35c4-4920-8259-110d9fa90e26))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0115") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 149.86 191.77 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d80960c)
    (property "Reference" "#PWR0116" (at 149.86 198.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 149.733 196.1642 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 149.86 191.77 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 149.86 191.77 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1b285335-1708-4221-97b6-4e0dbd7408f4))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0116") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:Crystal") (at 54.61 142.24 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d84ea8a)
    (property "Reference" "Y1" (at 57.9374 141.0716 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "25MHz" (at 57.9374 143.383 90)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Crystal:Crystal_HC18-U_Vertical" (at 54.61 142.24 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 54.61 142.24 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c0d9d720-c9fc-430b-9503-9ad39bac8e80))
    (pin "2" (uuid 53f202d8-d496-436b-a9fd-163d5e532318))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "Y1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 104.14 219.71 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d8539d9)
    (property "Reference" "#PWR0117" (at 104.14 226.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 104.267 224.1042 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 104.14 219.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 104.14 219.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 38b77428-8b0b-45cb-90a8-26108cd064b5))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0117") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 38.1 147.32 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d8780cb)
    (property "Reference" "#PWR0125" (at 38.1 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 38.227 151.7142 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 38.1 147.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 38.1 147.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 22a40740-6db5-46e0-9a53-bf2f378b555e))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0125") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 46.99 146.05 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d87955a)
    (property "Reference" "C6" (at 49.53 144.78 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "20pF" (at 50.8 147.32 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 43.18 147.0152 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 46.99 146.05 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ec02f72f-f71d-443c-8e9b-0bce0d931cb7))
    (pin "2" (uuid a565dac5-e5af-491d-9b37-0bb3462b5a1c))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C6") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 46.99 138.43 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005d879c6d)
    (property "Reference" "C5" (at 49.53 137.16 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "20pF" (at 50.8 139.7 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 43.18 139.3952 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 46.99 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 02eb058b-25bd-48cf-8640-1541ae7d24f6))
    (pin "2" (uuid bb003d1e-4baa-49e9-a607-cfd3a00ce3b1))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C5") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Conn_01x06_Male") (at 151.13 153.67 0) (mirror y) (unit 1)
    (in_bom no) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005dbd7ff5)
    (property "Reference" "J8" (at 151.8412 156.6672 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "SWD" (at 151.8412 154.3558 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x06_P2.54mm_Vertical" (at 151.13 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 151.13 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid a9903df4-cf72-4817-9769-7a835818bae7))
    (pin "2" (uuid baa1f9e3-be7e-44c3-b8e4-48fa3e562f71))
    (pin "3" (uuid 3aae9ce5-8970-4acd-af1c-066df7860f35))
    (pin "4" (uuid 446fec0a-646c-416d-95e4-912df1fe7cde))
    (pin "5" (uuid 22c73194-060a-4a01-b9e8-f9fb05b80829))
    (pin "6" (uuid aba71751-2a0a-40c1-bc46-952dcd879dfa))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J8") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 140.97 147.32 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005dc01901)
    (property "Reference" "#PWR0107" (at 140.97 151.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 141.351 142.9258 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 140.97 147.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 140.97 147.32 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5e47ad16-f2bc-4e49-813f-8cf673178e22))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0107") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 142.24 167.64 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005dc6b951)
    (property "Reference" "#PWR0108" (at 142.24 173.99 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 142.367 172.0342 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 142.24 167.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 142.24 167.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5c6832de-415b-4e02-961b-5cdb48feda18))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0108") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector_Generic:Conn_01x01") (at 384.81 38.1 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00006089865e)
    (property "Reference" "J9" (at 386.842 37.0332 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "Conn_01x01" (at 386.842 39.3446 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "MountingHole:MountingHole_4.3mm_M4_DIN965_Pad" (at 384.81 38.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 384.81 38.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6d39cb64-fe4d-4f17-9cef-f110fbd3f922))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J9") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 358.14 146.05 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00006097b4f4)
    (property "Reference" "R22" (at 352.8822 146.05 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 355.1936 146.05 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 359.918 146.05 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 358.14 146.05 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fd1f7baa-5dae-4e1f-8165-eadfdb21d87c))
    (pin "2" (uuid 6162f694-979f-4c06-8c16-4f296f5fec2b))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R22") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 353.06 163.83 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000609a4d4b)
    (property "Reference" "R16" (at 353.06 169.0878 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 353.06 166.7764 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 353.06 162.052 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 353.06 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid eb8a458d-1e4e-4066-98e7-3103c635f85f))
    (pin "2" (uuid b725b0ce-461e-4a5b-888a-8081ca1e8fd8))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R16") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 356.87 113.03 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000609a66db)
    (property "Reference" "R21" (at 351.6122 113.03 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 353.9236 113.03 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 358.648 113.03 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 356.87 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6a40d9cb-089c-4204-acee-53d0e009faa4))
    (pin "2" (uuid afc14de1-f68a-4513-92f1-70bdd3d9d6e0))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R21") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 351.79 130.81 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000609a6e97)
    (property "Reference" "R15" (at 351.79 136.0678 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 351.79 133.7564 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 351.79 129.032 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 351.79 130.81 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1eb40315-a645-4638-9d39-84d24458c0bf))
    (pin "2" (uuid 82598fb0-5f10-479a-ad53-26da93d031fe))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R15") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 349.25 85.09 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000060bf1a12)
    (property "Reference" "R12" (at 349.25 90.3478 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 349.25 88.0364 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 349.25 83.312 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 349.25 85.09 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 690340bf-abee-4030-b237-5f901e1be8b9))
    (pin "2" (uuid 25623cef-eea5-48a2-9fb5-9950a691b537))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R12") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 355.6 68.58 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000060bf2569)
    (property "Reference" "R20" (at 360.8578 68.58 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 358.5464 68.58 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 353.822 68.58 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 355.6 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dd488d04-2812-4120-afcd-2e3e089f5689))
    (pin "2" (uuid c07d9702-1991-4612-90ab-ddea0b78eb14))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R20") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 349.25 50.8 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000060bf2c1b)
    (property "Reference" "R11" (at 349.25 56.0578 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 349.25 53.7464 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 349.25 49.022 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 349.25 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 47fa301f-7391-4d02-badc-831793882d59))
    (pin "2" (uuid 5bf6c281-d00e-4e03-ac7d-c204d1ab2f0c))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R11") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 355.6 34.29 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000060bf37ad)
    (property "Reference" "R19" (at 360.8578 34.29 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "560" (at 358.5464 34.29 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 353.822 34.29 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 355.6 34.29 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid feb390f5-780f-46bd-bf7e-34105cb72e0a))
    (pin "2" (uuid 3cff355f-4467-442f-af03-c7a4b9c1492d))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R19") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 327.66 170.18 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00006174307f)
    (property "Reference" "#PWR0102" (at 327.66 176.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 327.787 174.5742 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 327.66 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 327.66 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4b2ad35e-49f0-4cf1-bf9a-a204be9bd718))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0102") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 367.03 53.34 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ebf73)
    (property "Reference" "D5" (at 367.03 47.8536 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 367.03 50.165 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 367.03 53.34 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 367.03 53.34 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e04b2931-c280-4b2b-8853-63ec69c4dad6))
    (pin "2" (uuid f30fde0d-c370-4e88-a6aa-fd08a0c0eefc))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D5") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ecbaa)
    (property "Reference" "D7" (at 368.3 82.1436 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 84.455 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5339ab5a-2f3d-4244-bd8f-8115d3ee90fd))
    (pin "2" (uuid 4acf46c7-62f8-4594-abf0-b2c2b5a3cc46))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D7") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 132.08 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ed2a1)
    (property "Reference" "D9" (at 368.3 126.5936 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 128.905 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 132.08 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 132.08 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid a9cb316f-d681-4700-ab01-e89e5030c0ec))
    (pin "2" (uuid 6e0e6926-b374-49e1-bd04-86ad5f39bbd1))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D9") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 165.1 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617edb91)
    (property "Reference" "D11" (at 368.3 159.6136 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 161.925 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 165.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 165.1 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b2f5d1bf-5fda-4ef3-9c38-23a7d58f798f))
    (pin "2" (uuid c80197b6-19bc-4ec8-976c-4697405a7c76))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D11") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 367.03 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ede53)
    (property "Reference" "D6" (at 367.03 62.23 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 367.03 59.69 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 367.03 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 367.03 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 294c5a88-95a9-47c1-9fad-22bdc61cc58f))
    (pin "2" (uuid 41cb4ede-c5ce-4afd-9d12-f6cd83153f8d))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D6") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 91.44 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ee4fb)
    (property "Reference" "D8" (at 368.3 96.52 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 60185039-f217-4de0-b3b8-aae246472f66))
    (pin "2" (uuid 62ef7668-9d0d-41bf-8e4a-d536d1b24689))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D8") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 135.89 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617eeb1e)
    (property "Reference" "D10" (at 368.3 140.97 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 138.43 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 135.89 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 135.89 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e28a905d-7e84-41c1-8e66-8b7469e48e30))
    (pin "2" (uuid 882b23a9-eca7-4099-a83c-88c1badbf80e))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D10") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:D_TVS") (at 368.3 168.91 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000617ef21a)
    (property "Reference" "D12" (at 368.3 173.99 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D_TVS" (at 368.3 171.45 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Diode_SMD:D_SMA_Handsoldering" (at 368.3 168.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 368.3 168.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6dc9c01d-7d10-48f8-93ac-00337805f008))
    (pin "2" (uuid 9f77f196-ad10-4a84-8629-938c97b9c7e8))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "D12") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 346.71 39.37 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000061e4b5f4)
    (property "Reference" "R7" (at 346.71 34.1122 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "130" (at 346.71 36.4236 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 346.71 41.148 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 346.71 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ca869630-20d8-469c-84d5-695c31b087ee))
    (pin "2" (uuid 2ea75034-a5c9-465f-b75b-ca1d4ae33280))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R7") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 347.98 73.66 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000061e4be22)
    (property "Reference" "R8" (at 347.98 68.4022 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "130" (at 347.98 70.7136 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 347.98 75.438 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 347.98 73.66 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d162f4d2-fad4-48d6-974e-dde6cc1f5177))
    (pin "2" (uuid 0517ddf0-54b1-4f94-8273-11bd519e9ad5))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R8") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 347.98 118.11 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000061e4c38f)
    (property "Reference" "R9" (at 347.98 112.8522 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "130" (at 347.98 115.1636 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 347.98 119.888 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 347.98 118.11 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b1f28974-c9e8-42e7-b670-1d34b4edf577))
    (pin "2" (uuid 2f62616d-d408-44dd-b838-253ee17ba8e0))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R9") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 347.98 149.86 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000061e4c9b0)
    (property "Reference" "R10" (at 347.98 144.6022 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "130" (at 347.98 146.9136 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 347.98 151.638 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 347.98 149.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3e2f50f2-a03c-484d-bf4e-e189d30a10c1))
    (pin "2" (uuid f19b0659-3d8e-4e2a-b552-d164bbc2935f))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R10") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_DIP_x04") (at 128.27 124.46 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 04765940-77d6-4b33-9c21-10ac3f1e29f0)
    (property "Reference" "SW2" (at 128.27 113.8301 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "SW_DIP_x04" (at 128.27 115.7511 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_DIP:DIP-8_W7.62mm" (at 128.27 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 128.27 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 12b0b428-d44c-44c1-9ad2-beaf0a83d20a))
    (pin "2" (uuid 003d66f0-e946-4f8e-8be5-c380c0758318))
    (pin "3" (uuid 1d5a08c7-d1f6-4b5c-bfd3-afd372d7f564))
    (pin "4" (uuid 41d17f46-8ba7-467b-b741-eb45a61649c7))
    (pin "5" (uuid 9a511455-1947-4d1f-96bc-57c54b02eb5a))
    (pin "6" (uuid 4b444d23-69c5-4ec7-bdd0-e74599dbcac6))
    (pin "7" (uuid 30a71ec4-bcee-451e-acf9-ef14edc42f47))
    (pin "8" (uuid 222a963f-e713-4ba5-b594-9d9ee0773e6d))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "SW2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 48.26 50.8 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 1b5d0e82-0dcd-4323-af65-b8bd0dec71f8)
    (property "Reference" "R3" (at 45.72 50.8 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "5K1" (at 50.8 50.8 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 46.482 50.8 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 48.26 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d3143f1a-04dd-4321-81ed-126e1efa4df4))
    (pin "2" (uuid c31036f0-523c-46c9-9960-19df00a54b6a))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 379.73 168.91 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 3b0e0920-86b4-4a1b-b25a-9201817c6a8c)
    (property "Reference" "#PWR0103" (at 379.73 175.26 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 379.857 173.3042 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 379.73 168.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 379.73 168.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d4b68afc-a56e-46d0-aaf5-35f4ed49ac4f))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0103") (unit 1)
        )
      )
    )
  )

  (symbol (lib_name "GND_1") (lib_id "power:GND") (at 167.64 198.12 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 43dd8a8d-c98e-492e-8750-ea3b18b88e91)
    (property "Reference" "#PWR04" (at 161.29 198.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 164.4651 198.4368 90)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 167.64 198.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 167.64 198.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9e4dc091-31f2-4543-a1f8-0faf23301236))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR04") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Switch:SW_Push") (at 149.86 129.54 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 4e49d060-473d-4eb0-8889-e59f13ab597d)
    (property "Reference" "SW3" (at 149.86 122.301 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "SW_Push" (at 149.86 124.6124 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Button_Switch_THT:SW_Tactile_SPST_Angled_PTS645Vx39-2LFS" (at 149.86 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 149.86 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d146c68d-8ec4-4d8a-b4c0-a25c934d9921))
    (pin "2" (uuid f5cce3f3-b22a-4ab0-b8f1-cbb164a59326))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "SW3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Power_Protection:NUP4202") (at 78.74 40.64 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 58190292-03f0-4fce-8e91-61b7eeb5dc5f)
    (property "Reference" "U1" (at 78.74 31.5935 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "ESDALD05UD4" (at 78.74 34.3686 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_TO_SOT_SMD:SOT-23-6_Handsoldering" (at 76.835 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://www.onsemi.com/pub_link/Collateral/NUP4202W1-D.PDF" (at 76.835 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 82217657-b2ec-4039-9d32-a62650b1b4da))
    (pin "2" (uuid 7d9b41f4-f248-42cc-ad88-8fb896b73ff5))
    (pin "3" (uuid bc3bab15-7d54-4ef8-8034-6f0710b5538e))
    (pin "4" (uuid 961e2ead-34af-44fd-9d19-8e44d73c571c))
    (pin "5" (uuid e4ff1a33-9ecd-48a6-bf60-22e0093e7cad))
    (pin "6" (uuid 7b6ab3ea-5e0f-422c-a523-0103a0e12e12))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 158.75 196.85 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 5cdce2fe-1eee-4796-a877-d539a2e50e2b)
    (property "Reference" "#PWR05" (at 158.75 200.66 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 158.369 192.4558 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 158.75 196.85 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 158.75 196.85 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6d87d10a-fe7e-4a8a-b608-a677a92c7fdb))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR05") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 137.16 116.84 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 6a05457f-5e72-406b-a347-2074c2db03a7)
    (property "Reference" "#PWR0104" (at 137.16 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 137.541 112.4458 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 137.16 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 137.16 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d9588019-bd3d-451f-b17e-497e7f14f5c9))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0104") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "CustomParts:STM32G441RB") (at 92.71 160.02 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 8a5e8b89-c319-442f-a653-f87e9da317ff)
    (property "Reference" "U3" (at 99.7459 212.5425 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "STM32G474RET" (at 99.7459 214.4635 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Package_QFP:LQFP-64_10x10mm_P0.5mm" (at 112.395 215.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://cdn-reichelt.de/documents/datenblatt/A300/STM32G441XB.pdf" (at 112.395 215.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fc67dfc9-458d-43b3-b550-73a90c233598))
    (pin "10" (uuid 8582d8a8-8666-4595-b783-db8ceb9f8742))
    (pin "11" (uuid 7e1a201c-effc-44ef-a6f6-0c156106fe69))
    (pin "12" (uuid 02aac6e8-86f5-40b9-8832-e0623390256a))
    (pin "13" (uuid 5f84203c-fa50-419b-85c5-cf733ffaebb1))
    (pin "14" (uuid c3aba3ba-1c5e-41e1-876e-e67bb7f4dffb))
    (pin "15" (uuid 15546f0e-7a80-41af-a3e4-99a7a8c7b676))
    (pin "16" (uuid c7b9390d-ef51-4522-a97a-3446bb022010))
    (pin "17" (uuid 0a3cf5a5-6e03-4e94-a5ab-be09bbb66b70))
    (pin "18" (uuid dabd8649-1fba-41c8-b4bd-d743714721f5))
    (pin "19" (uuid 6e265ec6-2368-4703-b551-420ecb5530de))
    (pin "2" (uuid 22fc3001-ccc9-4799-a197-cace1829cf56))
    (pin "20" (uuid 25a2e5b5-b52e-4752-8c53-42764f27bb27))
    (pin "21" (uuid a1a6289d-38c4-4826-a34a-01c8abf09b14))
    (pin "22" (uuid 43c8e238-d708-4989-b6eb-6bf5d801b07f))
    (pin "23" (uuid 70d4fb28-e842-454d-a879-39e52579517f))
    (pin "24" (uuid 3b65cb1e-5a26-4669-b1a2-072b696107e7))
    (pin "25" (uuid a3bf7523-0f5b-41a7-9b31-b874787e54ef))
    (pin "26" (uuid a5bbab54-398b-416c-a835-8ab886be74b5))
    (pin "27" (uuid 00334c5a-e46e-4cd4-924a-47f0b8e342bd))
    (pin "28" (uuid 958f2e24-2e55-4830-948a-e3aedc735d28))
    (pin "29" (uuid f56598c9-adfc-4acd-a50c-4eb4cb62eff8))
    (pin "3" (uuid 2758fb75-04a5-45fc-997e-ac3f50667553))
    (pin "30" (uuid a9c4d11e-551e-444f-8198-fe34a81e2936))
    (pin "31" (uuid 2aa15731-709e-40da-a306-5da3f0ade8e9))
    (pin "32" (uuid 5f3d1fe0-7a40-43de-adbf-bf950338edb2))
    (pin "33" (uuid 2ad47cb7-2e83-46dc-8238-c822230299ef))
    (pin "34" (uuid 7d2b19f2-da73-4970-baf9-579673d99fd3))
    (pin "35" (uuid c6f07f09-0dc1-4ce1-8fe0-f084de5bcbc2))
    (pin "36" (uuid faef4134-52ed-4c6d-9499-4f659c1ceac3))
    (pin "37" (uuid c797d92d-dab5-4be0-a184-3aab35038e0d))
    (pin "38" (uuid 756b0d12-883d-4a47-9773-55f69fbe3a37))
    (pin "39" (uuid abef60cc-c722-4112-b614-fb30ac705e65))
    (pin "4" (uuid 218dea24-9a48-4977-a7ee-d42ba66ae91c))
    (pin "40" (uuid 5ec95b91-c4b8-469f-a43e-d67cce08820e))
    (pin "41" (uuid c36696c1-6107-4afa-b198-bcd573dbc5fe))
    (pin "42" (uuid 8ec1ae87-5932-4de9-9765-60e27d916f93))
    (pin "43" (uuid 698094da-35e4-40e5-b4e1-2829ab43bee0))
    (pin "44" (uuid 3526446d-4269-4a6c-a747-4dae2d6d176d))
    (pin "45" (uuid d0ee402e-6081-4be1-b88d-516b917b334a))
    (pin "46" (uuid be5139dc-814c-49b0-9344-8ab41ece2f2f))
    (pin "47" (uuid e67511fe-b7e8-4cc3-8bcc-4b91c99719b9))
    (pin "48" (uuid 7ef07985-53ae-4394-baea-df6f0e698fb0))
    (pin "49" (uuid bc0b0f4f-1d85-4e48-b6b2-a4edede16db9))
    (pin "5" (uuid 7ffd9000-58d2-4c50-9185-a9b2d01fa149))
    (pin "50" (uuid 589ebebd-5cfb-4545-9afd-98a761b65063))
    (pin "51" (uuid f6c8eb41-ebd1-40cc-b123-97639e29a70d))
    (pin "52" (uuid c957d7e5-e7ed-445b-b7f2-6d53957d0457))
    (pin "53" (uuid 729e856b-9b1a-4571-9fda-9b5db85564ab))
    (pin "54" (uuid afd3e5d8-2bef-4e63-a052-6dc5d641538a))
    (pin "55" (uuid f207f045-8d47-47e9-8fb0-082bc4f63eb2))
    (pin "56" (uuid 1d5e277b-0255-488d-9ff8-fb1002462756))
    (pin "57" (uuid b2b360f7-404e-400f-83aa-a8b16433c294))
    (pin "58" (uuid 0556d176-0475-425c-84e3-867dac5df550))
    (pin "59" (uuid fe28d019-2901-415f-bbfb-9066de361fb0))
    (pin "6" (uuid 4c604759-f1ac-4423-8f19-0875c04cc87a))
    (pin "60" (uuid 9daf28e9-e434-4421-a163-df532f62633a))
    (pin "61" (uuid 5086e0da-250d-42a8-a43f-fced5afa4e61))
    (pin "62" (uuid ca6a6475-1ce9-4d3a-92ad-0b7481036c88))
    (pin "63" (uuid a68989b4-e61d-46e7-8dde-90ecb54a6c4c))
    (pin "64" (uuid 1ce3ff3f-5adf-48dc-b601-ac5d521256e0))
    (pin "7" (uuid f7974c72-13e5-4f48-84bc-d574810733c5))
    (pin "8" (uuid 7980ba85-a14d-4d69-be91-6e773d9329a2))
    (pin "9" (uuid aec4d929-2bfa-4e9c-946a-3a0d33e389ea))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 160.02 124.46 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 95f49ae9-0720-4cf4-829c-2ece986d1f0d)
    (property "Reference" "#PWR03" (at 160.02 128.27 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 159.639 120.0658 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 160.02 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 160.02 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 69014181-d6a3-4600-8d11-56f748da6eb9))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR03") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 40.64 99.06 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 96bb42af-1def-4f61-833c-8605225152c2)
    (property "Reference" "#PWR01" (at 40.64 102.87 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 41.021 94.6658 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 40.64 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 40.64 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b4cfab40-e366-4490-bfbf-c94c94ee3dcd))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR01") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:USB_C_Receptacle_USB2.0") (at 27.94 39.37 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 9b6f06b6-8cc8-4aa3-ae2e-e4e33a47a4f2)
    (property "Reference" "J1" (at 27.94 18.5801 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "USB_C_Receptacle_USB2.0" (at 27.94 20.5011 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Connector_USB:USB_C_Receptacle_HRO_TYPE-C-31-M-12" (at 31.75 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://www.usb.org/sites/default/files/documents/usb_type-c.zip" (at 31.75 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "A1" (uuid 8e6ff9dc-ed45-4061-b5ff-3025f3062492))
    (pin "A12" (uuid 71697578-e688-4379-872e-246fe099ec83))
    (pin "A4" (uuid e66ead0e-0615-4a38-9225-99628cda0a8c))
    (pin "A5" (uuid 527a3620-e2a7-4b3b-86ce-9da5344c8166))
    (pin "A6" (uuid 931baae1-8601-4580-956a-03c849c7192d))
    (pin "A7" (uuid f8148384-6e9e-4511-951b-d52a6315d273))
    (pin "A8" (uuid 50d715ae-62a8-46f5-aff4-8a4a860f3fe7))
    (pin "A9" (uuid daaffd91-6e1b-4825-bb38-8ab3f7af4f42))
    (pin "B1" (uuid 1856a1d6-1075-4a79-a2db-245684d84ba7))
    (pin "B12" (uuid df6496e2-2e2f-4867-83d1-8acd7b1ad3eb))
    (pin "B4" (uuid 63d14acb-f2c7-46d0-969d-c6578af9bfc7))
    (pin "B5" (uuid 4f1a395d-e46f-458c-8c4d-0d7f79fa11a3))
    (pin "B6" (uuid 28faf0cc-9599-4a27-bac7-e15e5150b100))
    (pin "B7" (uuid 7ed9cba1-7b04-49c2-b71b-f5e9b7ee0152))
    (pin "B8" (uuid 9776c617-1b80-4c88-bccd-a9c8c248df54))
    (pin "B9" (uuid 285a9c00-2aa3-4512-bac3-62dc47377318))
    (pin "S1" (uuid edfb9e64-489d-4590-b4c4-7eb281751d7c))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 64.77 128.27 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid a430060c-76ed-4d4b-8668-873762af6b41)
    (property "Reference" "C3" (at 64.77 125.73 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (at 64.77 130.81 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 65.7352 132.08 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 64.77 128.27 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7bf6b8ee-32b2-46e3-ad6b-1c20d2675738))
    (pin "2" (uuid 5421a35a-a9b0-432f-ba2e-61bfbf9272cf))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 58.42 124.46 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid a77690cd-07dc-461c-9760-052d3a19b3df)
    (property "Reference" "#PWR0106" (at 58.42 128.27 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 58.801 120.0658 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 58.42 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 58.42 124.46 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid a8ea7d18-f17c-4375-a788-f64880e030a9))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0106") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Connector:Conn_01x10_Pin") (at 172.72 203.2 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid bf66aad4-69bb-4913-b571-4bb17185d5aa)
    (property "Reference" "J2" (at 172.085 189.9539 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Subboard" (at 172.085 191.8749 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Connector_FFC-FPC:TE_1-84953-0_1x10-1MP_P1.0mm_Horizontal" (at 172.72 203.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 172.72 203.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 05327c2d-1049-49b6-b0d1-142a80da896f))
    (pin "10" (uuid 8f3088c2-fa17-41cc-9b87-a5c1e8115424))
    (pin "2" (uuid 05ef5862-8b41-4bf6-a4e0-d7d864a13ac6))
    (pin "3" (uuid d03a720b-5658-4f18-96e9-506982f30383))
    (pin "4" (uuid 72520787-073c-4607-8e98-d35be9dbb0a7))
    (pin "5" (uuid f74175fa-a7e2-44f2-9504-e0993ccd84fe))
    (pin "6" (uuid d0b2ab33-d3c0-4e50-bd75-4e0525dfa102))
    (pin "7" (uuid d1d23e8e-da73-4778-a9cf-f04a837634f6))
    (pin "8" (uuid afea2f53-cb20-46e3-8fac-bb37dc7400ae))
    (pin "9" (uuid 377171aa-1c63-4df4-834c-b7b034b962b6))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "J2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 149.86 138.43 90) (mirror x) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid c9177183-7e1a-4763-a614-15f899781194)
    (property "Reference" "C14" (at 149.86 132.0292 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "100nF" (at 149.86 134.3406 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 153.67 139.3952 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 149.86 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid bf6278c3-14d6-45cb-bd91-65df885faf04))
    (pin "2" (uuid 776cfe6d-bc93-4983-b30b-db80fcb30949))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C14") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 325.12 92.71 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid ca31b44c-8048-4511-8d17-3604fb3547e2)
    (property "Reference" "#PWR02" (at 325.12 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 325.247 97.1042 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 325.12 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 325.12 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dd4baf86-ada3-4108-bf08-2c437d113b6c))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR02") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:+3.3V") (at 325.12 30.48 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid eaccb5b5-4bdd-4eaa-a70f-5eccc9b929b1)
    (property "Reference" "#PWR0105" (at 325.12 34.29 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+3.3V" (at 325.501 26.0858 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 325.12 30.48 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 325.12 30.48 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c58059f8-647a-41eb-8820-2988f1828af1))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "#PWR0105") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Regulator_Linear:AP2112K-3.3") (at 106.68 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid ebda82ae-364f-4e99-aa6b-0858734a8248)
    (property "Reference" "U2" (at 106.68 24.2951 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "AP2112K-3.3" (at 106.68 26.2161 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5_HandSoldering" (at 106.68 23.495 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://www.diodes.com/assets/Datasheets/AP2112.pdf" (at 106.68 29.21 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f82c7b17-070a-403b-bc47-a78c0a4537fc))
    (pin "2" (uuid 424d28c4-393e-4628-8d23-79cbf57330bd))
    (pin "3" (uuid f27bf591-6a04-4b27-8045-388f7f1a8b2e))
    (pin "4" (uuid dda84252-1cfc-413b-adf6-599abe655a33))
    (pin "5" (uuid 8273d16b-afd8-4207-8e0a-ffbf4592ada6))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "U2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 119.38 35.56 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid f03ac4de-fb11-4cd4-8cdb-0b356b569ac6)
    (property "Reference" "C15" (at 122.301 34.3916 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1μF" (at 122.301 36.703 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 120.3452 39.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 119.38 35.56 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2ea497b3-5e7f-42db-aa02-eed6f4a4cf1e))
    (pin "2" (uuid d5332514-b53a-454d-8a40-fe8db430c35a))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C15") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 58.42 128.27 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid f245f049-4f7b-41a2-ac2a-6adc8cd10528)
    (property "Reference" "C2" (at 58.42 125.73 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1μF" (at 58.42 130.81 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (at 59.3852 132.08 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 58.42 128.27 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c236a2cb-0289-49dd-ab61-fade0de1f073))
    (pin "2" (uuid c11e69bc-42af-48ba-a647-981677302d15))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "C2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 55.88 50.8 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid f98a539b-2265-4434-8a16-015735e69060)
    (property "Reference" "R4" (at 53.34 50.8 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "5K1" (at 58.42 50.8 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Resistor_SMD:R_0603_1608Metric_Pad0.98x0.95mm_HandSolder" (at 54.102 50.8 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 55.88 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c493cbbc-9805-4b07-b1c7-0bd50b89fac0))
    (pin "2" (uuid 6369c7fe-3938-49cb-b429-64c005322cb8))
    (instances
      (project "DMX"
        (path "/535f7e00-920f-4f85-bbb1-ce11653908ad"
          (reference "R4") (unit 1)
        )
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
