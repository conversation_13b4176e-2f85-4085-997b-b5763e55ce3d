
Fuzzing the lwIP stack (afl-fuzz requires linux/unix or similar)

This directory contains a small app that reads Ethernet frames from stdin and
processes them. It is used together with the 'american fuzzy lop' tool (found
at http://lcamtuf.coredump.cx/afl/) and the sample inputs to test how
unexpected inputs are handled. The afl tool will read the known inputs, and
try to modify them to exercise as many code paths as possible, by instrumenting
the code and keeping track of which code is executed.

Just running make will produce the test program.

Running make with parameter 'D=-DLWIP_FUZZ_MULTI_PACKET' will produce a binary
that parses the input data as multiple packets (experimental!).

Then run afl with:

afl-fuzz -i inputs/<INPUT> -o output ./lwip_fuzz

and it should start working. It will probably complain about CPU scheduler,
set AFL_SKIP_CPUFREQ=1 to ignore it.
If it complains about invalid "/proc/sys/kernel/core_pattern" setting, try
executing "sudo bash -c 'echo core > /proc/sys/kernel/core_pattern'".

The input is split into different subdirectories since they test different
parts of the code, and since you want to run one instance of afl-fuzz on each
core.

When afl finds a crash or a hang, the input that caused it will be placed in
the output directory. If you have hexdump and text2pcap tools installed,
running output_to_pcap.sh <outputdir> will create pcap files for each input
file to simplify viewing in wireshark.

The lwipopts.h file needs to have checksum checking off, otherwise almost every
packet will be discarded because of that. The other options can be tuned to
expose different parts of the code.

