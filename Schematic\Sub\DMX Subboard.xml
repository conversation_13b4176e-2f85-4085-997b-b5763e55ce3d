<?xml version="1.0" encoding="UTF-8"?>
<export version="E">
  <design>
    <source>F:\Projekte\Lichtpult\Firmware\DMX G441\Schematic\Sub\DMX Subboard.kicad_sch</source>
    <date>29.05.2023 10:21:57</date>
    <tool>Eeschema 7.0.2</tool>
    <sheet number="1" name="/" tstamps="/">
      <title_block>
        <title/>
        <company/>
        <rev/>
        <date/>
        <source>DMX Subboard.kicad_sch</source>
        <comment number="1" value=""/>
        <comment number="2" value=""/>
        <comment number="3" value=""/>
        <comment number="4" value=""/>
        <comment number="5" value=""/>
        <comment number="6" value=""/>
        <comment number="7" value=""/>
        <comment number="8" value=""/>
        <comment number="9" value=""/>
      </title_block>
    </sheet>
  </design>
  <components>
    <comp ref="J1">
      <value>OLED</value>
      <footprint>Connector_PinHeader_2.54mm:PinHeader_1x04_P2.54mm_Vertical</footprint>
      <libsource lib="Connector" part="Conn_01x04_Pin" description="Generic connector, single row, 01x04, script generated"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Generic connector, single row, 01x04, script generated"/>
      <property name="ki_keywords" value="connector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>bb52d49e-060e-4e5c-b3b3-cf880a7397a2</tstamps>
    </comp>
    <comp ref="J2">
      <value>Connector</value>
      <footprint>Connector_FFC-FPC:TE_1-84952-0_1x10-1MP_P1.0mm_Horizontal</footprint>
      <libsource lib="Connector" part="Conn_01x10_Pin" description="Generic connector, single row, 01x10, script generated"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Generic connector, single row, 01x10, script generated"/>
      <property name="ki_keywords" value="connector"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>2a156e28-1f01-458b-a58f-f3d2a4352dcc</tstamps>
    </comp>
    <comp ref="SW1">
      <value>Back</value>
      <footprint>Button_Switch_THT:SW_PUSH_6mm_H8.5mm</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>1a011ca1-7233-4a4c-b58a-ec0d89d2a5a4</tstamps>
    </comp>
    <comp ref="SW2">
      <value>Up</value>
      <footprint>Button_Switch_THT:SW_PUSH_6mm_H8.5mm</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>af038ba5-c8d9-4e4b-ac5b-642d6c7155d8</tstamps>
    </comp>
    <comp ref="SW3">
      <value>Down</value>
      <footprint>Button_Switch_THT:SW_PUSH_6mm_H8.5mm</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>c162340e-07e6-42cf-863c-9b3a53b5ccc1</tstamps>
    </comp>
    <comp ref="SW4">
      <value>Confirm</value>
      <footprint>Button_Switch_THT:SW_PUSH_6mm_H8.5mm</footprint>
      <libsource lib="Switch" part="SW_Push" description="Push button switch, generic, two pins"/>
      <property name="Sheetname" value=""/>
      <property name="Sheetfile" value="DMX Subboard.kicad_sch"/>
      <property name="ki_description" value="Push button switch, generic, two pins"/>
      <property name="ki_keywords" value="switch normally-open pushbutton push-button"/>
      <sheetpath names="/" tstamps="/"/>
      <tstamps>8e27603f-08f9-4a80-a479-0ec4540de0f6</tstamps>
    </comp>
  </components>
  <libparts>
    <libpart lib="Connector" part="Conn_01x04_Pin">
      <description>Generic connector, single row, 01x04, script generated</description>
      <docs>~</docs>
      <footprints>
        <fp>Connector*:*_1x??_*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Conn_01x04_Pin</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
        <pin num="2" name="Pin_2" type="passive"/>
        <pin num="3" name="Pin_3" type="passive"/>
        <pin num="4" name="Pin_4" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Connector" part="Conn_01x10_Pin">
      <description>Generic connector, single row, 01x10, script generated</description>
      <docs>~</docs>
      <footprints>
        <fp>Connector*:*_1x??_*</fp>
      </footprints>
      <fields>
        <field name="Reference">J</field>
        <field name="Value">Conn_01x10_Pin</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="Pin_1" type="passive"/>
        <pin num="2" name="Pin_2" type="passive"/>
        <pin num="3" name="Pin_3" type="passive"/>
        <pin num="4" name="Pin_4" type="passive"/>
        <pin num="5" name="Pin_5" type="passive"/>
        <pin num="6" name="Pin_6" type="passive"/>
        <pin num="7" name="Pin_7" type="passive"/>
        <pin num="8" name="Pin_8" type="passive"/>
        <pin num="9" name="Pin_9" type="passive"/>
        <pin num="10" name="Pin_10" type="passive"/>
      </pins>
    </libpart>
    <libpart lib="Switch" part="SW_Push">
      <description>Push button switch, generic, two pins</description>
      <docs>~</docs>
      <fields>
        <field name="Reference">SW</field>
        <field name="Value">SW_Push</field>
        <field name="Datasheet">~</field>
      </fields>
      <pins>
        <pin num="1" name="1" type="passive"/>
        <pin num="2" name="2" type="passive"/>
      </pins>
    </libpart>
  </libparts>
  <libraries>
    <library logical="Connector">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Connector.kicad_sym</uri>
    </library>
    <library logical="Switch">
      <uri>C:\Program Files\KiCad\7.0\share\kicad\symbols\/Switch.kicad_sym</uri>
    </library>
  </libraries>
  <nets>
    <net code="1" name="+3.3V">
      <node ref="J1" pin="1" pinfunction="Pin_1" pintype="passive"/>
      <node ref="J2" pin="4" pinfunction="Pin_4" pintype="passive"/>
      <node ref="J2" pin="9" pinfunction="Pin_9" pintype="passive"/>
      <node ref="SW1" pin="2" pinfunction="2" pintype="passive"/>
      <node ref="SW2" pin="2" pinfunction="2" pintype="passive"/>
      <node ref="SW3" pin="2" pinfunction="2" pintype="passive"/>
      <node ref="SW4" pin="2" pinfunction="2" pintype="passive"/>
    </net>
    <net code="2" name="/SCL">
      <node ref="J1" pin="3" pinfunction="Pin_3" pintype="passive"/>
      <node ref="J2" pin="2" pinfunction="Pin_2" pintype="passive"/>
    </net>
    <net code="3" name="/SDA">
      <node ref="J1" pin="4" pinfunction="Pin_4" pintype="passive"/>
      <node ref="J2" pin="1" pinfunction="Pin_1" pintype="passive"/>
    </net>
    <net code="4" name="GND">
      <node ref="J1" pin="2" pinfunction="Pin_2" pintype="passive"/>
      <node ref="J2" pin="3" pinfunction="Pin_3" pintype="passive"/>
    </net>
    <net code="5" name="Net-(J2-Pin_5)">
      <node ref="J2" pin="5" pinfunction="Pin_5" pintype="passive"/>
      <node ref="SW1" pin="1" pinfunction="1" pintype="passive"/>
    </net>
    <net code="6" name="Net-(J2-Pin_6)">
      <node ref="J2" pin="6" pinfunction="Pin_6" pintype="passive"/>
      <node ref="SW4" pin="1" pinfunction="1" pintype="passive"/>
    </net>
    <net code="7" name="Net-(J2-Pin_7)">
      <node ref="J2" pin="7" pinfunction="Pin_7" pintype="passive"/>
      <node ref="SW2" pin="1" pinfunction="1" pintype="passive"/>
    </net>
    <net code="8" name="Net-(J2-Pin_8)">
      <node ref="J2" pin="8" pinfunction="Pin_8" pintype="passive"/>
      <node ref="SW3" pin="1" pinfunction="1" pintype="passive"/>
    </net>
    <net code="9" name="unconnected-(J2-Pin_10-Pad10)">
      <node ref="J2" pin="10" pinfunction="Pin_10" pintype="passive"/>
    </net>
  </nets>
</export>
