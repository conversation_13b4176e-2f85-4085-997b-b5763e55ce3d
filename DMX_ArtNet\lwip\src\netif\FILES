This directory contains generic network interface device drivers that
do not contain any hardware or architecture specific code. The files
are:

ethernet.c
          Shared code for Ethernet based interfaces.

lowpan6.c
          A 6LoWPAN implementation as a netif.

lowpan6_ble.c
          A 6LoWPAN over Bluetooth Low Energy (BLE) implementation as netif,
          according to RFC-7668.

slipif.c
          A generic implementation of the SLIP (Serial Line IP)
          protocol. It requires a sio (serial I/O) module to work.

ppp/      Point-to-Point Protocol stack
          The lwIP PPP support is based from pppd (http://ppp.samba.org) with
          huge changes to match code size and memory requirements for embedded
          devices. Please read /doc/ppp.txt and ppp/PPPD_FOLLOWUP for a detailed
          explanation.
