<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="84.456mm"
   height="24.434126622294162mm"
   viewBox="0 0 84.456 24.434126622294162"
   version="1.1"
   id="svg23"
   sodipodi:docname="Cover-Back.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <metadata
     id="metadata29">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs27" />
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="2560"
     inkscape:window-height="1369"
     id="namedview25"
     showgrid="false"
     inkscape:zoom="6.2026175"
     inkscape:cx="259.3619"
     inkscape:cy="47.321233"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg23" />
  <g
     id="Sketch001"
     transform="translate(42.228000,12.217063) scale(1,-1)"
     style="fill:#000000;fill-opacity:1;stroke:none">
    <circle
       cx="0.0"
       cy="0.0"
       r="11.2"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle2" />
    <circle
       cx="9.9"
       cy="9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle4" />
    <circle
       cx="-9.9"
       cy="-9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle6" />
    <circle
       cx="30.0"
       cy="0.0"
       r="11.2"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle8" />
    <circle
       cx="-30.0"
       cy="0.0"
       r="11.2"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle10" />
    <circle
       cx="-20.1"
       cy="9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle12" />
    <circle
       cx="-39.9"
       cy="-9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle14" />
    <circle
       cx="20.1"
       cy="-9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle16" />
    <circle
       cx="39.9"
       cy="9.9"
       r="1.5"
       stroke="#000000"
       stroke-width="0.35 px"
       style="stroke-width:0.35;stroke-miterlimit:4;stroke-dasharray:none;stroke-linecap:square;fill:#000000;fill-opacity:1;stroke:none"
       id="circle18" />
    <title
       id="title20">b'Back'</title>
  </g>
</svg>
